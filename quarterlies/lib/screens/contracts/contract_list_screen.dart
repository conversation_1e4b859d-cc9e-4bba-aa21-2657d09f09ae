import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_list_tile.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/screens/contracts/contract_detail_screen.dart';
import 'package:quarterlies/screens/contracts/contract_form_screen.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'dart:async';

class ContractListScreen extends StatefulWidget {
  const ContractListScreen({super.key});

  @override
  State<ContractListScreen> createState() => _ContractListScreenState();
}

class _ContractListScreenState extends State<ContractListScreen> {
  final DataRepository _dataRepository = DataRepository();
  List<Contract> _contracts = [];
  String? _errorMessage;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  StreamSubscription<bool>? _connectivitySubscription;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _loadContracts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
          _loadContracts(); // Reload data when back online
        } else {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadContracts() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadContracts',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final contracts = await _dataRepository.getContracts();
          if (!mounted) return;

          setState(() {
            _contracts = contracts;
          });
        },
        message: 'Loading contracts...',
        errorMessage: 'Failed to load contracts',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load contracts: ${e.toString()}';
        });
      }
    }
  }

  void _filterContracts(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  List<Contract> get _filteredContracts {
    if (_searchQuery.isEmpty) {
      return _contracts;
    }

    return _contracts.where((contract) {
      // Search by contract ID
      if (contract.id.toLowerCase().contains(_searchQuery)) {
        return true;
      }

      // Search by status
      if (contract.status.toLowerCase().contains(_searchQuery)) {
        return true;
      }

      // Search by notes
      if (contract.notes != null &&
          contract.notes!.toLowerCase().contains(_searchQuery)) {
        return true;
      }

      return false;
    }).toList();
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case ContractStatus.draft:
        return Colors.grey;
      case ContractStatus.sent:
        return Colors.blue;
      case ContractStatus.signed:
        return Colors.green;
      case ContractStatus.completed:
        return Colors.purple;
      case ContractStatus.cancelled:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Contracts'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Search Contracts',
                hintText: 'Search by ID, status, or notes',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                suffixIcon:
                    _searchQuery.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _filterContracts('');
                          },
                        )
                        : null,
              ),
              onChanged: _filterContracts,
            ),
          ),
          Expanded(
            child: Consumer<LoadingStateProvider>(
              builder: (context, loadingProvider, child) {
                final isLoading = loadingProvider.isLoading('loadContracts');

                return isLoading
                    ? const Center(
                      child: QuarterliesLoadingIndicator(
                        message: 'Loading contracts...',
                        size: 32.0,
                      ),
                    )
                    : _errorMessage != null
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadContracts,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                    : _filteredContracts.isEmpty
                    ? const Center(child: Text('No contracts found'))
                    : Consumer<DisplaySettingsProvider>(
                      builder: (context, displayProvider, child) {
                        return RefreshIndicator(
                          onRefresh: _loadContracts,
                          child: ListView.builder(
                            padding: EdgeInsets.symmetric(
                              horizontal:
                                  displayProvider.isOfficeMode ? 8.0 : 16.0,
                              vertical:
                                  displayProvider.isOfficeMode ? 4.0 : 8.0,
                            ),
                            itemCount: _filteredContracts.length,
                            itemBuilder: (context, index) {
                              final contract = _filteredContracts[index];
                              return AdaptiveListTile(
                                title: Text(
                                  'Contract #${contract.id.substring(0, 8)}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: Text(
                                  'Amount: \$${contract.totalAmount.toStringAsFixed(2)}',
                                ),
                                leading: const Icon(
                                  Icons.description,
                                  color: Colors.purple,
                                ),
                                trailing: Chip(
                                  label: Text(contract.status),
                                  backgroundColor: _getStatusColor(
                                    contract.status,
                                  ),
                                ),
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => ContractDetailScreen(
                                            contractId: contract.id,
                                          ),
                                    ),
                                  ).then((_) => _loadContracts());
                                },
                                // Additional info shown only in Office Mode
                                additionalInfo:
                                    displayProvider.isOfficeMode
                                        ? OfficeAdditionalInfo(
                                          items: [
                                            InfoItem(
                                              label: 'Created',
                                              value: DateFormat(
                                                'MM/dd/yyyy',
                                              ).format(contract.createdAt),
                                              icon: Icons.calendar_today,
                                            ),
                                            InfoItem(
                                              label: 'Items',
                                              value:
                                                  '${contract.lineItems.length} items',
                                              icon: Icons.list,
                                            ),
                                            if (contract.notes != null &&
                                                contract.notes!.isNotEmpty)
                                              InfoItem(
                                                label: 'Notes',
                                                value:
                                                    contract.notes!.length > 30
                                                        ? '${contract.notes!.substring(0, 30)}...'
                                                        : contract.notes!,
                                                icon: Icons.note,
                                              ),
                                          ],
                                        )
                                        : null,
                                // Office actions shown only in Office Mode
                                officeActions:
                                    displayProvider.isOfficeMode
                                        ? [
                                          OfficeActionButton(
                                            icon: Icons.edit,
                                            label: 'Edit',
                                            onPressed: () async {
                                              final result =
                                                  await Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder:
                                                          (context) =>
                                                              ContractFormScreen(
                                                                contract:
                                                                    contract,
                                                              ),
                                                    ),
                                                  );
                                              if (result == true) {
                                                _loadContracts();
                                              }
                                            },
                                          ),
                                          OfficeActionButton(
                                            icon: Icons.send,
                                            label: 'Send',
                                            onPressed: () {
                                              ErrorDisplay.showInfo(
                                                context,
                                                'Send contract coming soon!',
                                              );
                                            },
                                          ),
                                        ]
                                        : null,
                              );
                            },
                          ),
                        );
                      },
                    );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ContractFormScreen()),
          ).then((_) => _loadContracts());
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
