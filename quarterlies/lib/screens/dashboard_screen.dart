import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/screens/tax_payments/tax_payment_form_screen.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/financial_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
// The models.dart file already includes ExpenseCategory class

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  bool _isOffline = false; // Added for offline mode indicator

  @override
  void initState() {
    super.initState();
    _checkConnectivity();
    // Load dashboard data through provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<FinancialProvider>(
        context,
        listen: false,
      ).refreshDashboardData();
    });
  }

  Future<void> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    setState(() {
      _isOffline = connectivityResult.contains(ConnectivityResult.none);
    });

    Connectivity().onConnectivityChanged.listen((result) {
      final wasOffline = _isOffline;
      setState(() {
        _isOffline = result.contains(ConnectivityResult.none);
      });

      // Show sync feedback when connectivity status changes
      if (mounted) {
        if (!_isOffline && wasOffline) {
          ErrorDisplay.showSync(context, FeedbackMessages.backOnline);
          _loadDashboardData(); // Refresh data when back online
        } else if (_isOffline && !wasOffline) {
          ErrorDisplay.showSync(
            context,
            FeedbackMessages.workingOffline,
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadDashboardData() async {
    try {
      // Delegate to FinancialProvider
      await Provider.of<FinancialProvider>(
        context,
        listen: false,
      ).refreshDashboardData();

      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Dashboard data refreshed successfully',
        );
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(context, 'Failed to refresh dashboard data');
      }
    }
  }

  // Categorize expenses by IRS Schedule C categories
  Map<String, double> _categorizeExpensesByScheduleC(List<Expense> expenses) {
    final Map<String, double> categoryTotals = {};

    // Initialize all categories with zero
    for (var category in ExpenseCategory.values) {
      categoryTotals[category] = 0.0;
    }

    // Add a category for uncategorized expenses
    categoryTotals['Uncategorized'] = 0.0;

    // Sum expenses by category
    for (var expense in expenses) {
      final category = expense.category;
      if (category != null && categoryTotals.containsKey(category)) {
        categoryTotals[category] =
            (categoryTotals[category] ?? 0.0) + expense.amount;
      } else {
        categoryTotals['Uncategorized'] =
            (categoryTotals['Uncategorized'] ?? 0.0) + expense.amount;
      }
    }

    return categoryTotals;
  }

  // This method is not used but kept for future reference
  // double _calculateTaxEstimate(double income, double expenses) {
  //   // Simple tax estimate calculation (can be refined based on actual tax rules)
  //   final taxableIncome = income - expenses;
  //   if (taxableIncome <= 0) return 0.0;
  //
  //   // Simplified tax rate of 25% for self-employment
  //   return taxableIncome * 0.25;
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
            tooltip: 'Refresh data',
          ),
        ],
      ),
      body: ResponsiveLayout(
        useSafeArea: false, // AppBar already handles safe area
        child: Column(
          children: [
            if (_isOffline)
              Container(
                width: double.infinity,
                color: Colors.red,
                padding: const EdgeInsets.all(8.0),
                child: ResponsiveBody(
                  'You are offline. Some data may not be up-to-date.',
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                ),
              ),
            Expanded(
              child: Consumer<FinancialProvider>(
                builder: (context, financialProvider, child) {
                  if (financialProvider.isLoading) {
                    return const Center(
                      child: QuarterliesLoadingIndicator(
                        message: 'Loading dashboard data...',
                        size: 32.0,
                      ),
                    );
                  }

                  if (financialProvider.errorMessage != null) {
                    return Center(
                      child: ResponsiveBody(
                        financialProvider.errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: _loadDashboardData,
                    child: Consumer<DisplaySettingsProvider>(
                      builder: (context, displayProvider, child) {
                        return SingleChildScrollView(
                          padding: EdgeInsets.all(
                            displayProvider.isOfficeMode ? 12.0 : 16.0,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildFinancialSummarySection(
                                displayProvider,
                                financialProvider,
                              ),
                              SizedBox(
                                height: displayProvider.isOfficeMode ? 16 : 24,
                              ),
                              _buildActiveJobsSection(
                                displayProvider,
                                financialProvider,
                              ),
                              SizedBox(
                                height: displayProvider.isOfficeMode ? 16 : 24,
                              ),
                              _buildInvoicesAndEstimatesSection(
                                displayProvider,
                                financialProvider,
                              ),
                              SizedBox(
                                height: displayProvider.isOfficeMode ? 16 : 24,
                              ),
                              _buildTaxSection(
                                displayProvider,
                                financialProvider,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummarySection(
    DisplaySettingsProvider displayProvider,
    FinancialProvider financialProvider,
  ) {
    return AdaptiveDetailCard(
      padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: ResponsiveTitle(
                  'Year-to-Date Summary',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Flexible(
                child: ResponsiveLabel(
                  'YTD ${DateTime.now().year}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
            ],
          ),
          const Divider(),
          SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),

          // In Office Mode, show more compact layout
          if (displayProvider.isOfficeMode) ...[
            _buildCompactFinancialGrid(financialProvider),
          ] else ...[
            // Field Mode - existing layout
            _buildFinancialRow(
              'Income',
              financialProvider.ytdIncome,
              Colors.green,
            ),
            const SizedBox(height: 8),
            _buildFinancialRow(
              'Job Expenses',
              financialProvider.ytdJobExpenses,
              Colors.red,
            ),
            const SizedBox(height: 4),
            _buildFinancialRow(
              'Overhead Expenses',
              financialProvider.ytdOverheadExpenses,
              Colors.red,
            ),
            const SizedBox(height: 4),
            _buildFinancialRow(
              'Total Expenses',
              financialProvider.ytdTotalExpenses,
              Colors.red,
            ),
            const SizedBox(height: 8),
            const Divider(),
            _buildFinancialRow(
              'Net Profit',
              financialProvider.ytdNetProfit,
              financialProvider.ytdNetProfit >= 0 ? Colors.green : Colors.red,
              isBold: true,
            ),
          ],

          SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
          if (displayProvider.isOfficeMode)
            ElevatedButton.icon(
              onPressed: () => _showScheduleCExpenseBreakdown(context),
              icon: const Icon(Icons.receipt_long, size: 16),
              label: const Text(
                'Schedule C Breakdown',
                style: TextStyle(fontSize: 12),
              ),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
            )
          else
            TextButton.icon(
              onPressed: () => _showScheduleCExpenseBreakdown(context),
              icon: const Icon(Icons.receipt_long),
              label: const Text('View Schedule C Expense Breakdown'),
            ),
        ],
      ),
    );
  }

  Widget _buildCompactFinancialGrid(FinancialProvider financialProvider) {
    final formatter = NumberFormat.currency(symbol: '\$');

    return Column(
      children: [
        // Income and Net Profit row
        Row(
          children: [
            Expanded(
              child: _buildCompactFinancialCard(
                'Income',
                formatter.format(financialProvider.ytdIncome),
                Colors.green,
                Icons.trending_up,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactFinancialCard(
                'Net Profit',
                formatter.format(financialProvider.ytdNetProfit),
                financialProvider.ytdNetProfit >= 0 ? Colors.green : Colors.red,
                financialProvider.ytdNetProfit >= 0
                    ? Icons.trending_up
                    : Icons.trending_down,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Expenses row
        Row(
          children: [
            Expanded(
              child: _buildCompactFinancialCard(
                'Job Expenses',
                formatter.format(financialProvider.ytdJobExpenses),
                Colors.red,
                Icons.work,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildCompactFinancialCard(
                'Overhead',
                formatter.format(financialProvider.ytdOverheadExpenses),
                Colors.orange,
                Icons.business,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCompactFinancialCard(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: ResponsiveLabel(
                  label,
                  style: TextStyle(fontWeight: FontWeight.w500, color: color),
                  minFontSize: 9.0,
                  maxFontSize: 13.0,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ResponsiveBody(
            value,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
            maxLines: 1,
            minFontSize: 12.0,
            maxFontSize: 16.0,
          ),
        ],
      ),
    );
  }

  // Show a dialog with Schedule C expense breakdown
  void _showScheduleCExpenseBreakdown(BuildContext context) {
    // Get expenses for the current year
    final currentYear = DateTime.now().year;
    final startOfYear = DateTime(currentYear, 1, 1);

    // Use a separate method to handle the async part
    _loadAndShowExpenseBreakdown(startOfYear, context);
  }

  // Load expense data and show dialog
  Future<void> _loadAndShowExpenseBreakdown(
    DateTime startDate,
    BuildContext dialogContext,
  ) async {
    // Store the context in a local variable to avoid BuildContext across async gaps
    final BuildContext localContext = dialogContext;

    try {
      final dataRepository = DataRepository();
      final expenses = await dataRepository.getExpensesSince(startDate);

      if (!mounted) return;

      final categoryTotals = _categorizeExpensesByScheduleC(expenses);

      // Sort categories by amount (highest first)
      final sortedCategories =
          categoryTotals.entries.toList()
            ..sort((a, b) => b.value.compareTo(a.value));

      // Filter out categories with zero expenses
      final nonZeroCategories =
          sortedCategories.where((entry) => entry.value > 0).toList();

      // Now it's safe to use the local context
      if (mounted && localContext.mounted) {
        showDialog(
          context: localContext,
          builder:
              (context) => AlertDialog(
                title: Text(
                  'Schedule C Expense Breakdown - ${DateTime.now().year}',
                ),
                content: SizedBox(
                  width: double.maxFinite,
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: nonZeroCategories.length,
                    itemBuilder: (context, index) {
                      final entry = nonZeroCategories[index];
                      final formatter = NumberFormat.currency(symbol: '\$');

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(child: Text(entry.key)),
                            Text(
                              formatter.format(entry.value),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Close'),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      if (mounted && localContext.mounted) {
        ScaffoldMessenger.of(localContext).showSnackBar(
          SnackBar(content: Text('Failed to load expense data: $e')),
        );
      }
    }
  }

  Widget _buildFinancialRow(
    String label,
    double amount,
    Color valueColor, {
    bool isBold = false,
  }) {
    final formatter = NumberFormat.currency(symbol: '\$');
    final style = TextStyle(
      fontSize: isBold ? 18 : 16,
      fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
      color: valueColor,
    );

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isBold ? 18 : 16,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(formatter.format(amount), style: style),
      ],
    );
  }

  Widget _buildActiveJobsSection(
    DisplaySettingsProvider displayProvider,
    FinancialProvider financialProvider,
  ) {
    return AdaptiveDetailCard(
      padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Active Jobs',
            style: TextStyle(
              fontSize: displayProvider.isOfficeMode ? 16 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Divider(),
          financialProvider.activeJobs.isEmpty
              ? Padding(
                padding: EdgeInsets.symmetric(
                  vertical: displayProvider.isOfficeMode ? 12.0 : 16.0,
                ),
                child: const Center(child: Text('No active jobs')),
              )
              : ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount:
                    displayProvider.isOfficeMode
                        ? financialProvider.activeJobs.length
                        : (financialProvider.activeJobs.length > 3
                            ? 3
                            : financialProvider.activeJobs.length),
                itemBuilder: (context, index) {
                  final jobData = financialProvider.activeJobs[index];
                  final jobTitle = jobData['title'] as String;
                  final jobStatus = jobData['status'] as String;
                  final profitLoss = jobData['profit'] as double;
                  final formatter = NumberFormat.currency(symbol: '\$');

                  return ListTile(
                    title: Text(
                      jobTitle,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Text('Status: $jobStatus'),
                    trailing: Text(
                      formatter.format(profitLoss),
                      style: TextStyle(
                        color: profitLoss >= 0 ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    onTap: () {
                      // Navigate to job details
                      Navigator.pushNamed(
                        context,
                        '/jobs/details',
                        arguments: jobData['id'] as String,
                      );
                    },
                  );
                },
              ),
          if (!displayProvider.isOfficeMode &&
              financialProvider.activeJobs.length > 3)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: TextButton(
                onPressed: () {
                  // Navigate to jobs list
                  Navigator.pushNamed(context, '/jobs');
                },
                child: const Text('View all jobs'),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInvoicesAndEstimatesSection(
    DisplaySettingsProvider displayProvider,
    FinancialProvider financialProvider,
  ) {
    return AdaptiveDetailCard(
      padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Invoices & Estimates',
            style: TextStyle(
              fontSize: displayProvider.isOfficeMode ? 16 : 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Divider(),
          SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),

          if (displayProvider.isOfficeMode) ...[
            // Office Mode: Compact grid layout
            Row(
              children: [
                Expanded(
                  child: _buildCompactStatusCard(
                    'Open Estimates',
                    financialProvider.openEstimates,
                    Colors.blue,
                    Icons.description,
                    () => Navigator.pushNamed(context, '/estimates'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildCompactStatusCard(
                    'Open Invoices',
                    financialProvider.openInvoices,
                    Colors.orange,
                    Icons.receipt_long,
                    () => Navigator.pushNamed(context, '/invoices'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildCompactStatusCard(
              'Overdue Invoices',
              financialProvider.overdueInvoices,
              Colors.red,
              Icons.warning,
              () => Navigator.pushNamed(
                context,
                '/invoices',
                arguments: {'filter': 'overdue'},
              ),
            ),
          ] else ...[
            // Field Mode: Existing layout
            _buildStatusRow(
              'Open Estimates',
              financialProvider.openEstimates,
              Colors.blue,
              () {
                Navigator.pushNamed(context, '/estimates');
              },
            ),
            const SizedBox(height: 12),
            _buildStatusRow(
              'Open Invoices',
              financialProvider.openInvoices,
              Colors.orange,
              () {
                Navigator.pushNamed(context, '/invoices');
              },
            ),
            const SizedBox(height: 12),
            _buildStatusRow(
              'Overdue Invoices',
              financialProvider.overdueInvoices,
              Colors.red,
              () {
                Navigator.pushNamed(
                  context,
                  '/invoices',
                  arguments: {'filter': 'overdue'},
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompactStatusCard(
    String label,
    int count,
    Color color,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 16, color: color),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(
    String label,
    int count,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Text(label, style: const TextStyle(fontSize: 16)),
              ],
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: color.withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                count.toString(),
                style: TextStyle(color: color, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaxSection(
    DisplaySettingsProvider displayProvider,
    FinancialProvider financialProvider,
  ) {
    final dateFormatter = DateFormat('MMMM d, yyyy');
    final remainingTax = financialProvider.remainingTaxOwed;

    return AdaptiveDetailCard(
      padding: EdgeInsets.all(displayProvider.isOfficeMode ? 12.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  'Quarterly Tax Estimate',
                  style: TextStyle(
                    fontSize: displayProvider.isOfficeMode ? 16 : 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Flexible(
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: displayProvider.isOfficeMode ? 6 : 8,
                    vertical: displayProvider.isOfficeMode ? 3 : 4,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    financialProvider.currentTaxPeriod,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                      fontSize: displayProvider.isOfficeMode ? 11 : 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
          const Divider(),
          SizedBox(height: displayProvider.isOfficeMode ? 6 : 8),

          if (displayProvider.isOfficeMode) ...[
            // Office Mode: Compact grid layout
            Row(
              children: [
                Expanded(
                  child: _buildCompactFinancialCard(
                    'Estimated',
                    NumberFormat.currency(
                      symbol: '\$',
                    ).format(financialProvider.currentTaxEstimate),
                    Colors.orange,
                    Icons.calculate,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildCompactFinancialCard(
                    'Paid',
                    NumberFormat.currency(
                      symbol: '\$',
                    ).format(financialProvider.taxPaymentsMade),
                    Colors.green,
                    Icons.payment,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            _buildCompactFinancialCard(
              'Remaining',
              NumberFormat.currency(symbol: '\$').format(remainingTax),
              remainingTax > 0 ? Colors.red : Colors.green,
              remainingTax > 0 ? Icons.warning : Icons.check_circle,
            ),
          ] else ...[
            // Field Mode: Existing layout
            _buildFinancialRow(
              'Estimated Tax',
              financialProvider.currentTaxEstimate,
              Colors.orange,
            ),
            const SizedBox(height: 8),
            _buildFinancialRow(
              'Payments Made',
              financialProvider.taxPaymentsMade,
              Colors.green,
            ),
            const SizedBox(height: 8),
            const Divider(),
            _buildFinancialRow(
              'Remaining',
              remainingTax,
              remainingTax > 0 ? Colors.red : Colors.green,
              isBold: true,
            ),
          ],

          SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: displayProvider.isOfficeMode ? 14 : 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Next due: ${dateFormatter.format(financialProvider.nextTaxDueDate)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: displayProvider.isOfficeMode ? 12 : 14,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => TaxPaymentFormScreen(
                          initialTaxPeriod: financialProvider.currentTaxPeriod,
                          suggestedAmount: remainingTax > 0 ? remainingTax : 0,
                        ),
                  ),
                );
              },
              icon: Icon(
                Icons.payment,
                size: displayProvider.isOfficeMode ? 16 : 20,
              ),
              label: Text(
                'Record Tax Payment',
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 12 : 14,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  vertical: displayProvider.isOfficeMode ? 10 : 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
