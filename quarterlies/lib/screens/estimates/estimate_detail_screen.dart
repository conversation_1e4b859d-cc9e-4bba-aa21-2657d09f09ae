import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/estimate_pdf_service.dart';
import 'package:quarterlies/services/document_signing_service.dart';
import 'package:quarterlies/services/document_signature_service.dart';
import 'package:quarterlies/screens/contracts/contract_form_screen.dart';
import 'package:quarterlies/screens/document_signing/create_signing_request_screen.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'estimate_form_screen.dart';

class EstimateDetailScreen extends StatefulWidget {
  final String estimateId;

  const EstimateDetailScreen({super.key, required this.estimateId});

  @override
  State<EstimateDetailScreen> createState() => _EstimateDetailScreenState();
}

class _EstimateDetailScreenState extends State<EstimateDetailScreen> {
  final DataRepository _dataRepository = DataRepository();
  final EstimatePdfService _estimatePdfService = EstimatePdfService();
  final DocumentSigningService _documentSigningService =
      DocumentSigningService();

  Estimate? _estimate;
  Job? _job;
  Customer? _customer;
  String? _errorMessage;
  bool _isDocumentSigned = false;

  @override
  void initState() {
    super.initState();
    _loadEstimateData();
  }

  Future<void> _checkIfDocumentSigned() async {
    try {
      final isSigned = await _documentSigningService.isDocumentSigned(
        widget.estimateId,
        'estimate',
      );

      if (mounted) {
        setState(() {
          _isDocumentSigned = isSigned;
        });
      }
    } catch (e) {
      debugPrint('Error checking if document is signed: $e');
    }
  }

  Future<void> _loadEstimateData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadEstimateData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get estimate details
          final estimate = await _dataRepository.getEstimateById(
            widget.estimateId,
          );

          if (estimate == null) {
            setState(() {
              _errorMessage = 'Estimate not found';
            });
            return;
          }

          // Get job details
          final job = await _dataRepository.getJobById(estimate.jobId);

          // Get customer details
          final customer = await _dataRepository.getCustomerById(
            estimate.customerId,
          );

          if (!mounted) return;

          setState(() {
            _estimate = estimate;
            _job = job;
            _customer = customer;
          });

          // Check if the document is signed
          await _checkIfDocumentSigned();
        },
        message: 'Loading estimate data...',
        errorMessage: 'Failed to load estimate data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load estimate data: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _updateEstimateStatus(String status) async {
    try {
      final updatedEstimate = _estimate!.copyWith(status: status);
      await _dataRepository.updateEstimate(updatedEstimate);

      // If estimate is accepted, update job estimated price
      if (status == EstimateStatus.accepted) {
        final updatedJob = _job!.copyWith(
          estimatedPrice: updatedEstimate.totalAmount,
        );
        await _dataRepository.updateJob(updatedJob);
      }

      _loadEstimateData();

      if (mounted) {
        ErrorDisplay.showOperation(
          context,
          'Estimate status updated to $status',
        );
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to update estimate: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _saveAsTemplate() async {
    // Show dialog to get template name
    final templateName = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Save as Template'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Enter a name for this template:'),
                const SizedBox(height: 16),
                TextField(
                  autofocus: true,
                  decoration: const InputDecoration(
                    labelText: 'Template Name',
                    border: OutlineInputBorder(),
                  ),
                  onSubmitted: (value) => Navigator.of(context).pop(value),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  final textController = TextEditingController();
                  showDialog<String>(
                    context: context,
                    builder:
                        (context) => AlertDialog(
                          title: const Text('Save as Template'),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text('Enter a name for this template:'),
                              const SizedBox(height: 16),
                              TextField(
                                controller: textController,
                                autofocus: true,
                                decoration: const InputDecoration(
                                  labelText: 'Template Name',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ],
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('Cancel'),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(textController.text);
                              },
                              child: const Text('Save'),
                            ),
                          ],
                        ),
                  );
                },
                child: const Text('Save'),
              ),
            ],
          ),
    );

    // If user cancelled, return
    if (templateName == null || templateName.isEmpty) return;

    try {
      // Create template
      await _dataRepository.createEstimateTemplate(_estimate!, templateName);

      if (mounted) {
        ErrorDisplay.showDataOperation(context, 'Template', 'created');
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to create template: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _convertToActiveJob() async {
    try {
      // Update estimate status to accepted if not already
      if (_estimate!.status != EstimateStatus.accepted) {
        await _updateEstimateStatus(EstimateStatus.accepted);
      }

      // Update job status to active
      final updatedJob = _job!.copyWith(
        status: 'active',
        estimatedPrice: _estimate!.totalAmount,
        startDate: DateTime.now(),
      );

      await _dataRepository.updateJob(updatedJob);

      _loadEstimateData();

      if (mounted) {
        ErrorDisplay.showOperation(context, 'Job converted to active status');
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to convert job: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _convertToContract() async {
    try {
      // Update estimate status to accepted if not already
      if (_estimate!.status != EstimateStatus.accepted) {
        await _updateEstimateStatus(EstimateStatus.accepted);
      }

      // Navigate to contract form screen with the estimate
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ContractFormScreen(estimate: _estimate),
          ),
        ).then((_) => _loadEstimateData());
      }
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Failed to create contract: ${e.toString()}',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_job?.title ?? 'Estimate Details'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EstimateFormScreen(estimate: _estimate),
                ),
              ).then((_) => _loadEstimateData());
            },
          ),
          // Electronic signature button
          Consumer<LoadingStateProvider>(
            builder: (context, loadingProvider, child) {
              final isGeneratingPdf = loadingProvider.isGeneratingPdf;
              return IconButton(
                icon: Icon(
                  Icons.draw,
                  color: _isDocumentSigned ? Colors.green : null,
                ),
                tooltip: 'Request Electronic Signature',
                onPressed: isGeneratingPdf ? null : _requestElectronicSignature,
              );
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'saveTemplate') {
                _saveAsTemplate();
              }
            },
            itemBuilder:
                (BuildContext context) => [
                  const PopupMenuItem<String>(
                    value: 'saveTemplate',
                    child: Row(
                      children: [
                        Icon(Icons.save_outlined),
                        SizedBox(width: 8),
                        Text('Save as Template'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadEstimateData');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading estimate data...',
                  size: 32.0,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadEstimateData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _estimate == null
              ? const Center(child: Text('Estimate not found'))
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 12.0 : 16.0,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Estimate header section
                        AdaptiveDetailSection(
                          title: 'Estimate Details',
                          icon: Icons.description,
                          alwaysExpandedInOffice: true,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    'Estimate for ${_job?.title ?? 'Unknown Job'}',
                                    style: TextStyle(
                                      fontSize:
                                          displayProvider.isOfficeMode
                                              ? 16
                                              : 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                Chip(
                                  label: Text(_estimate!.status),
                                  backgroundColor: _getStatusColor(
                                    _estimate!.status,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: displayProvider.isOfficeMode ? 8 : 12,
                            ),

                            if (displayProvider.isOfficeMode) ...[
                              // Office Mode: Compact grid layout
                              Row(
                                children: [
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Customer',
                                      value: _customer?.name ?? 'Unknown',
                                      icon: Icons.person,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Total Amount',
                                      value:
                                          '\$${_estimate!.totalAmount.toStringAsFixed(2)}',
                                      icon: Icons.attach_money,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Created',
                                      value: DateFormat(
                                        'MM/dd/yyyy',
                                      ).format(_estimate!.createdAt),
                                      icon: Icons.calendar_today,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: AdaptiveInfoRow(
                                      label: 'Last Updated',
                                      value:
                                          _estimate!.updatedAt != null
                                              ? DateFormat(
                                                'MM/dd/yyyy',
                                              ).format(_estimate!.updatedAt!)
                                              : 'N/A',
                                      icon: Icons.update,
                                    ),
                                  ),
                                ],
                              ),
                            ] else ...[
                              // Field Mode: Existing layout
                              Text('Customer: ${_customer?.name ?? 'Unknown'}'),
                              Text(
                                'Created: ${_formatDate(_estimate!.createdAt)}',
                              ),
                              Text(
                                'Last Updated: ${_estimate!.updatedAt != null ? _formatDate(_estimate!.updatedAt!) : 'N/A'}',
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'Total Amount: \$${_estimate!.totalAmount.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ],
                        ),

                        // Status action buttons
                        if (_estimate!.status == EstimateStatus.draft ||
                            _estimate!.status == EstimateStatus.sent)
                          Card(
                            margin: const EdgeInsets.only(bottom: 16),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Estimate Actions',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      if (_estimate!.status ==
                                          EstimateStatus.draft)
                                        ElevatedButton.icon(
                                          icon: const Icon(Icons.send),
                                          label: const Text('Mark as Sent'),
                                          onPressed:
                                              () => _updateEstimateStatus(
                                                EstimateStatus.sent,
                                              ),
                                        ),
                                      if (_estimate!.status ==
                                              EstimateStatus.sent ||
                                          _estimate!.status ==
                                              EstimateStatus.draft)
                                        ElevatedButton.icon(
                                          icon: const Icon(Icons.check_circle),
                                          label: const Text('Accept'),
                                          onPressed:
                                              () => _updateEstimateStatus(
                                                EstimateStatus.accepted,
                                              ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.green,
                                            foregroundColor: Colors.white,
                                          ),
                                        ),
                                      if (_estimate!.status ==
                                              EstimateStatus.sent ||
                                          _estimate!.status ==
                                              EstimateStatus.draft)
                                        ElevatedButton.icon(
                                          icon: const Icon(Icons.cancel),
                                          label: const Text('Reject'),
                                          onPressed:
                                              () => _updateEstimateStatus(
                                                EstimateStatus.rejected,
                                              ),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.red,
                                            foregroundColor: Colors.white,
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),

                        // Convert to active job button (only for accepted estimates)
                        if (_estimate!.status == EstimateStatus.accepted &&
                            _job?.status == 'estimate')
                          Card(
                            margin: const EdgeInsets.only(bottom: 16),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Job Actions',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Column(
                                    children: [
                                      SizedBox(
                                        width: double.infinity,
                                        child: ElevatedButton.icon(
                                          icon: const Icon(Icons.engineering),
                                          label: const Text(
                                            'Convert to Active Job',
                                          ),
                                          onPressed: _convertToActiveJob,
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                            foregroundColor: Colors.white,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      SizedBox(
                                        width: double.infinity,
                                        child: ElevatedButton.icon(
                                          icon: const Icon(Icons.description),
                                          label: const Text('Create Contract'),
                                          onPressed: _convertToContract,
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.green,
                                            foregroundColor: Colors.white,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),

                        // Line items
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Line Items',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: _estimate!.lineItems.length,
                                  itemBuilder: (context, index) {
                                    final item = _estimate!.lineItems[index];
                                    return ListTile(
                                      title: Text(item.description),
                                      subtitle: Text(
                                        '${item.quantity} ${item.unit} @ \$${item.unitPrice.toStringAsFixed(2)}',
                                      ),
                                      trailing: Text(
                                        '\$${item.total.toStringAsFixed(2)}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                                const Divider(),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'Total',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Text(
                                        '\$${_estimate!.totalAmount.toStringAsFixed(2)}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Notes section
                        if (_estimate!.notes != null &&
                            _estimate!.notes!.isNotEmpty)
                          Card(
                            margin: const EdgeInsets.only(top: 16),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Notes',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(_estimate!.notes!),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  );
                },
              );
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return Colors.grey[300]!;
      case 'sent':
        return Colors.blue[100]!;
      case 'accepted':
        return Colors.green[100]!;
      case 'rejected':
        return Colors.red[100]!;
      default:
        return Colors.grey[300]!;
    }
  }

  /// Request electronic signature for the estimate
  Future<void> _requestElectronicSignature() async {
    // Validate required data is available
    if (_estimate == null || _customer == null || _job == null) {
      if (!mounted) return;
      ErrorDisplay.showWarning(
        context,
        'Cannot request signature: Missing estimate data',
      );
      return;
    }

    // Check if user has a signature before allowing signing request
    final documentSignatureService = DocumentSignatureService();
    if (!await documentSignatureService.hasUserSignature()) {
      if (!mounted) return;

      // Use centralized feedback system
      ErrorDisplay.showWarning(
        context,
        'You must create your signature before sending estimates for signing',
      );
      return;
    }

    if (!mounted) return;
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithPdfLoading(() async {
        // Generate PDF document
        final pdfBytes = await _estimatePdfService.generateEstimatePdf(
          estimate: _estimate!,
          customer: _customer!,
          job: _job!,
        );

        if (!mounted) return;

        // Show PDF generation success feedback
        ErrorDisplay.showOperation(context, FeedbackMessages.pdfGenerated);

        // Navigate to the create signing request screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => CreateSigningRequestScreen(
                  documentType: 'estimate',
                  documentId: _estimate!.id,
                  documentBytes: pdfBytes,
                  customer: _customer!,
                  job: _job!,
                ),
          ),
        ).then((_) {
          // Refresh the document signed status when returning from the signing request screen
          _checkIfDocumentSigned();
        });
      }, documentType: 'Estimate');
    } catch (e) {
      if (mounted) {
        ErrorDisplay.showWarning(
          context,
          'Error generating PDF: ${e.toString()}',
        );
      }
    }
  }
}
