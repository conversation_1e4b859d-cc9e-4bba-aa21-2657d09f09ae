import 'package:flutter/material.dart';
import 'dart:async';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/recent_data_service.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:uuid/uuid.dart';
import 'package:quarterlies/services/voice_recording_service.dart';

class EstimateFormScreen extends StatefulWidget {
  final Estimate? estimate; // Null for new estimate, non-null for editing
  final String? jobId; // Optional job ID when creating from job screen
  final String? initialCustomerId; // For quick-add with customer selection
  final Map<String, dynamic>?
  initialDefaults; // Smart defaults from recent entries

  const EstimateFormScreen({
    super.key,
    this.estimate,
    this.jobId,
    this.initialCustomerId,
    this.initialDefaults,
  });

  @override
  State<EstimateFormScreen> createState() => _EstimateFormScreenState();
}

class _EstimateFormScreenState extends State<EstimateFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  final _dataRepository = DataRepository();
  final _recentDataService = RecentDataService();

  String? _selectedJobId;
  String? _selectedCustomerId;
  List<Job> _jobs = [];
  List<Customer> _customers = [];
  List<Estimate> _templates = [];
  List<EstimateItem> _lineItems = [];

  String? _errorMessage;
  bool _isOffline = false; // Added field to track offline status

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  bool get _isEditing => widget.estimate != null;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _loadInitialData();
  }

  @override
  void dispose() {
    _notesController.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(
            context,
            'Back online. Data will sync automatically.',
          );
        } else {
          ErrorDisplay.showSync(
            context,
            'Working offline. Changes saved locally.',
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadInitialData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    // Set initial customer ID from quick-add selection
    if (widget.initialCustomerId != null) {
      _selectedCustomerId = widget.initialCustomerId;
      // Update customer usage count
      if (_selectedCustomerId != null) {
        _recentDataService.updateCustomerUsage(_selectedCustomerId!);
      }
    }

    // Apply smart defaults if provided
    if (widget.initialDefaults != null &&
        widget.initialDefaults!.containsKey('line_items')) {
      try {
        final lineItemsData =
            widget.initialDefaults!['line_items'] as List<dynamic>;
        _lineItems =
            lineItemsData.map((item) => EstimateItem.fromJson(item)).toList();
      } catch (e) {
        // Silently handle parsing errors
      }
    }

    try {
      await loadingProvider.executeWithLoading(
        'loadEstimateData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Load jobs
          final jobs = await _dataRepository.getJobs();

          // Load customers
          final customers = await _dataRepository.getCustomers();

          // Load estimate templates
          final templates = await _dataRepository.getEstimateTemplates();

          if (!mounted) return;

          setState(() {
            _jobs = jobs;
            _customers = customers;
            _templates = templates;
          });

          // If editing, populate form with existing data
          if (_isEditing) {
            _populateFormWithExistingData();
          } else if (widget.jobId != null) {
            // If creating from job screen, pre-select the job
            _selectedJobId = widget.jobId;
            _preSelectCustomerFromJob();
          }
        },
        message: 'Loading estimate data...',
        errorMessage: 'Failed to load estimate data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load data: ${e.toString()}';
        });
      }
    }
  }

  void _populateFormWithExistingData() {
    _selectedJobId = widget.estimate!.jobId;
    _selectedCustomerId = widget.estimate!.customerId;
    _notesController.text = widget.estimate!.notes ?? '';
    _lineItems = List.from(widget.estimate!.lineItems);
  }

  Future<void> _preSelectCustomerFromJob() async {
    if (_selectedJobId != null) {
      try {
        final job = await _dataRepository.getJobById(_selectedJobId!);
        if (job != null) {
          setState(() {
            _selectedCustomerId = job.customerId;
          });
        }
      } catch (e) {
        // Handle error silently
      }
    }
  }

  Future<void> _saveEstimate() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedJobId == null) {
      ErrorDisplay.showWarning(context, 'Please select a job');
      return;
    }
    if (_selectedCustomerId == null) {
      ErrorDisplay.showWarning(context, 'Please select a customer');
      return;
    }
    if (_lineItems.isEmpty) {
      ErrorDisplay.showWarning(context, 'Please add at least one line item');
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveEstimate',
        () async {
          final userId = await _dataRepository.getCurrentUserId();
          final totalAmount = _calculateTotal();

          if (_isEditing) {
            // Update existing estimate
            final updatedEstimate = widget.estimate!.copyWith(
              jobId: _selectedJobId,
              customerId: _selectedCustomerId,
              lineItems: _lineItems,
              totalAmount: totalAmount,
              notes:
                  _notesController.text.isNotEmpty
                      ? _notesController.text
                      : null,
              updatedAt: DateTime.now(),
            );

            await _dataRepository.updateEstimate(updatedEstimate);
          } else {
            // Create new estimate
            final newEstimate = Estimate(
              id: const Uuid().v4(),
              userId: userId,
              jobId: _selectedJobId!,
              customerId: _selectedCustomerId!,
              lineItems: _lineItems,
              totalAmount: totalAmount,
              status: 'draft',
              notes:
                  _notesController.text.isNotEmpty
                      ? _notesController.text
                      : null,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            await _dataRepository.addEstimate(newEstimate);
          }

          if (mounted) {
            // Show success feedback
            final operation = _isEditing ? 'update' : 'save';
            ErrorDisplay.showDataOperation(
              context,
              'estimate',
              operation,
              isOffline: _isOffline,
            );
            Navigator.pop(context, true);
          }
        },
        message: _isEditing ? 'Updating estimate...' : 'Creating estimate...',
        errorMessage: 'Failed to save estimate',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to save estimate: ${e.toString()}';
        });
      }
    }
  }

  double _calculateTotal() {
    return _lineItems.fold(0, (sum, item) => sum + item.total);
  }

  void _addLineItem() {
    showDialog(
      context: context,
      builder:
          (context) => LineItemDialog(
            onSave: (item) {
              setState(() {
                _lineItems.add(item);
              });
            },
          ),
    );
  }

  void _editLineItem(int index) {
    showDialog(
      context: context,
      builder:
          (context) => LineItemDialog(
            item: _lineItems[index],
            onSave: (item) {
              setState(() {
                _lineItems[index] = item;
              });
            },
          ),
    );
  }

  void _removeLineItem(int index) {
    setState(() {
      _lineItems.removeAt(index);
    });
  }

  void _useTemplate(Estimate template) {
    setState(() {
      _lineItems = template.lineItems.map((item) => item.clone()).toList();
      _notesController.text = template.notes ?? '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Estimate' : 'New Estimate'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadEstimateData');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading estimate data...',
                  size: 32.0,
                  showOfflineStatus: true,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadInitialData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : Column(
                children: [
                  if (_isOffline)
                    Container(
                      color: Colors.orange,
                      padding: const EdgeInsets.all(8),
                      child: const Text(
                        'You are offline. Some features may be unavailable.',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  Expanded(
                    child: Consumer<DisplaySettingsProvider>(
                      builder: (context, displayProvider, child) {
                        return Form(
                          key: _formKey,
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(
                              displayProvider.isOfficeMode ? 12 : 16,
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Basic Information Section
                                AdaptiveFormSection(
                                  title: 'Basic Information',
                                  icon: Icons.info,
                                  children: [
                                    if (displayProvider.isOfficeMode) ...[
                                      // Office Mode: Job and Customer in same row
                                      Row(
                                        children: [
                                          Expanded(
                                            child: DropdownButtonFormField<
                                              String
                                            >(
                                              decoration: const InputDecoration(
                                                labelText: 'Job',
                                                border: OutlineInputBorder(),
                                              ),
                                              value: _selectedJobId,
                                              items:
                                                  _jobs.map((job) {
                                                    return DropdownMenuItem<
                                                      String
                                                    >(
                                                      value: job.id,
                                                      child: Text(job.title),
                                                    );
                                                  }).toList(),
                                              onChanged: (value) {
                                                setState(() {
                                                  _selectedJobId = value;
                                                  final job = _jobs.firstWhere(
                                                    (job) => job.id == value,
                                                  );
                                                  _selectedCustomerId =
                                                      job.customerId;
                                                });
                                              },
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Please select a job';
                                                }
                                                return null;
                                              },
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: DropdownButtonFormField<
                                              String
                                            >(
                                              decoration: const InputDecoration(
                                                labelText: 'Customer',
                                                border: OutlineInputBorder(),
                                              ),
                                              value: _selectedCustomerId,
                                              items:
                                                  _customers.map((customer) {
                                                    return DropdownMenuItem<
                                                      String
                                                    >(
                                                      value: customer.id,
                                                      child: Text(
                                                        customer.name,
                                                      ),
                                                    );
                                                  }).toList(),
                                              onChanged: (value) {
                                                setState(() {
                                                  _selectedCustomerId = value;
                                                });
                                              },
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Please select a customer';
                                                }
                                                return null;
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ] else ...[
                                      // Field Mode: Separate fields
                                      DropdownButtonFormField<String>(
                                        decoration: const InputDecoration(
                                          labelText: 'Job',
                                          border: OutlineInputBorder(),
                                        ),
                                        value: _selectedJobId,
                                        items:
                                            _jobs.map((job) {
                                              return DropdownMenuItem<String>(
                                                value: job.id,
                                                child: Text(job.title),
                                              );
                                            }).toList(),
                                        onChanged: (value) {
                                          setState(() {
                                            _selectedJobId = value;
                                            final job = _jobs.firstWhere(
                                              (job) => job.id == value,
                                            );
                                            _selectedCustomerId =
                                                job.customerId;
                                          });
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please select a job';
                                          }
                                          return null;
                                        },
                                      ),
                                      DropdownButtonFormField<String>(
                                        decoration: const InputDecoration(
                                          labelText: 'Customer',
                                          border: OutlineInputBorder(),
                                        ),
                                        value: _selectedCustomerId,
                                        items:
                                            _customers.map((customer) {
                                              return DropdownMenuItem<String>(
                                                value: customer.id,
                                                child: Text(customer.name),
                                              );
                                            }).toList(),
                                        onChanged: (value) {
                                          setState(() {
                                            _selectedCustomerId = value;
                                          });
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please select a customer';
                                          }
                                          return null;
                                        },
                                      ),
                                    ],
                                  ],
                                ),

                                // Templates section
                                if (_templates.isNotEmpty) ...[
                                  const Text(
                                    'Use Template',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  SizedBox(
                                    height: 60,
                                    child: ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: _templates.length,
                                      itemBuilder: (context, index) {
                                        final template = _templates[index];
                                        return Padding(
                                          padding: const EdgeInsets.only(
                                            right: 8,
                                          ),
                                          child: ElevatedButton(
                                            onPressed:
                                                () => _useTemplate(template),
                                            child: Text(
                                              'Template ${index + 1}',
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                ],

                                // Line items section
                                const Text(
                                  'Line Items',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Card(
                                  child: Column(
                                    children: [
                                      ListView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: _lineItems.length,
                                        itemBuilder: (context, index) {
                                          final item = _lineItems[index];
                                          return ListTile(
                                            title: Text(item.description),
                                            subtitle: Text(
                                              '${item.quantity} ${item.unit} @ \$${item.unitPrice.toStringAsFixed(2)}',
                                            ),
                                            trailing: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  '\$${item.total.toStringAsFixed(2)}',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                IconButton(
                                                  icon: const Icon(Icons.edit),
                                                  onPressed:
                                                      () =>
                                                          _editLineItem(index),
                                                ),
                                                IconButton(
                                                  icon: const Icon(
                                                    Icons.delete,
                                                  ),
                                                  onPressed:
                                                      () => _removeLineItem(
                                                        index,
                                                      ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            ElevatedButton.icon(
                                              icon: const Icon(Icons.add),
                                              label: const Text('Add Item'),
                                              onPressed: _addLineItem,
                                            ),
                                            Text(
                                              'Total: \$${_calculateTotal().toStringAsFixed(2)}',
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // Notes field
                                TextFormField(
                                  controller: _notesController,
                                  decoration: const InputDecoration(
                                    labelText: 'Notes',
                                    hintText: 'Enter any additional notes',
                                    border: OutlineInputBorder(),
                                  ),
                                  keyboardType: TextInputType.multiline,
                                  maxLines: null,
                                ),
                                const SizedBox(height: 16),

                                // Save button
                                Consumer<LoadingStateProvider>(
                                  builder: (context, loadingProvider, child) {
                                    return SizedBox(
                                      width: double.infinity,
                                      child: CustomButton(
                                        text:
                                            _isEditing
                                                ? 'Update Estimate'
                                                : 'Create Estimate',
                                        onPressed: _saveEstimate,
                                        isLoading: loadingProvider.isLoading(
                                          'saveEstimate',
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              );
        },
      ),
    );
  }
}

// Dialog for adding/editing line items
class LineItemDialog extends StatefulWidget {
  final EstimateItem? item;
  final Function(EstimateItem) onSave;

  const LineItemDialog({super.key, this.item, required this.onSave});

  @override
  State<LineItemDialog> createState() => _LineItemDialogState();
}

class _LineItemDialogState extends State<LineItemDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _quantityController = TextEditingController();
  final _unitController = TextEditingController();
  final _unitPriceController = TextEditingController();
  final _taxRateController = TextEditingController();
  final _voiceRecordingService = VoiceRecordingService();

  bool _isRecording = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    if (widget.item != null) {
      _descriptionController.text = widget.item!.description;
      _quantityController.text = widget.item!.quantity.toString();
      _unitController.text = widget.item!.unit;
      _unitPriceController.text = widget.item!.unitPrice.toString();
      _taxRateController.text = widget.item!.taxRate?.toString() ?? '';
    } else {
      // Default values for new items
      _unitController.text = 'hours';
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _quantityController.dispose();
    _unitController.dispose();
    _unitPriceController.dispose();
    _taxRateController.dispose();
    super.dispose();
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to start recording: ${e.toString()}';
      });
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text specifically for line item information
        final extractedInfo = _voiceRecordingService.processLineItemVoiceInput(
          transcribedText,
        );

        setState(() {
          // Apply extracted information to form fields
          if (extractedInfo.containsKey('description')) {
            _descriptionController.text = extractedInfo['description'];
          }
          if (extractedInfo.containsKey('quantity')) {
            _quantityController.text = extractedInfo['quantity'];
          }
          if (extractedInfo.containsKey('unitPrice') ||
              extractedInfo.containsKey('price')) {
            _unitPriceController.text =
                extractedInfo['unitPrice'] ?? extractedInfo['price'];
          }
          if (extractedInfo.containsKey('unit')) {
            _unitController.text = extractedInfo['unit'];
          }
        });

        // Show voice operation feedback
        if (mounted) {
          ErrorDisplay.showOperation(
            context,
            'Voice recording processed successfully',
          );
        }
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to process recording: ${e.toString()}';
        _isRecording = false;
      });
    }
  }

  void _saveItem() {
    if (_formKey.currentState!.validate()) {
      final item = EstimateItem(
        id: widget.item?.id,
        description: _descriptionController.text,
        quantity: double.parse(_quantityController.text),
        unit: _unitController.text,
        unitPrice: double.parse(_unitPriceController.text),
        taxRate:
            _taxRateController.text.isNotEmpty
                ? double.parse(_taxRateController.text)
                : null,
      );
      widget.onSave(item);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.item == null ? 'Add Line Item' : 'Edit Line Item'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_errorMessage != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              // Description field with voice recording button
              Stack(
                alignment: Alignment.centerRight,
                children: [
                  TextFormField(
                    controller: _descriptionController,
                    decoration: InputDecoration(
                      labelText: 'Description',
                      border: const OutlineInputBorder(),
                      hintText:
                          'Enter item description or tap mic to use voice',
                      suffixIcon:
                          _isRecording
                              ? Container(
                                margin: const EdgeInsets.only(right: 32),
                                child: const Icon(Icons.mic, color: Colors.red),
                              )
                              : null,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a description';
                      }
                      return null;
                    },
                  ),
                  Positioned(
                    right: 8,
                    child: IconButton(
                      icon: Icon(
                        _isRecording ? Icons.stop : Icons.mic,
                        color:
                            _isRecording
                                ? Colors.red
                                : Theme.of(context).colorScheme.primary,
                      ),
                      onPressed:
                          _isRecording ? _stopRecording : _startRecording,
                      tooltip:
                          _isRecording
                              ? 'Stop recording'
                              : 'Record line item details',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _quantityController,
                      decoration: const InputDecoration(
                        labelText: 'Quantity',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      controller: _unitController,
                      decoration: const InputDecoration(
                        labelText: 'Unit',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _unitPriceController,
                      decoration: const InputDecoration(
                        labelText: 'Unit Price (\$)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Required';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Invalid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      controller: _taxRateController,
                      decoration: const InputDecoration(
                        labelText: 'Tax Rate (%)',
                        border: OutlineInputBorder(),
                        hintText: 'Optional',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (double.tryParse(value) == null) {
                            return 'Invalid number';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        if (!_isRecording)
          TextButton.icon(
            icon: const Icon(Icons.mic),
            label: const Text('Voice'),
            onPressed: _startRecording,
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.secondary,
            ),
          ),
        ElevatedButton(onPressed: _saveItem, child: const Text('Save')),
      ],
    );
  }
}
