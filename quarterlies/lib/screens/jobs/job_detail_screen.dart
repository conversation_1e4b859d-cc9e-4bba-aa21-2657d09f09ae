import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/screens/estimates/estimate_form_screen.dart';
import 'package:quarterlies/screens/estimates/estimate_detail_screen.dart';
import 'package:quarterlies/screens/expenses/expense_form_screen.dart';
import 'package:quarterlies/screens/expenses/expense_detail_screen.dart';
import 'package:quarterlies/screens/time_logs/time_log_form_screen.dart';
import 'package:quarterlies/screens/time_logs/time_log_detail_screen.dart';
import 'package:quarterlies/screens/invoices/invoice_form_screen.dart';
import 'package:quarterlies/screens/invoices/invoice_detail_screen.dart';
import 'package:quarterlies/screens/payments/payment_form_screen.dart';
import 'package:quarterlies/screens/payments/payment_detail_screen.dart';

import 'package:quarterlies/widgets/budget_chart.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/services/budget_service.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/services/report_service.dart';
import 'package:quarterlies/models/report_models.dart' as report_models;
import 'package:share_plus/share_plus.dart';
import 'job_form_screen.dart';
import 'package:intl/intl.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/feedback_messages.dart';
import 'package:quarterlies/utils/error_handler.dart';

class JobDetailScreen extends StatefulWidget {
  final String jobId;

  const JobDetailScreen({super.key, required this.jobId});

  @override
  State<JobDetailScreen> createState() => _JobDetailScreenState();
}

class _JobDetailScreenState extends State<JobDetailScreen>
    with SingleTickerProviderStateMixin {
  final SupabaseService _supabaseService = SupabaseService();
  final DataRepository _dataRepository = DataRepository();
  late TabController _tabController;
  Job? _job;
  Customer? _customer;
  Map<String, dynamic>? _jobData;
  String? _errorMessage;

  // Controller for notes tab
  late TextEditingController _notesController;
  bool _isEditing = false;

  // Report generation state
  bool _isGeneratingReport = false;
  late ReportService _reportService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
    _notesController = TextEditingController();
    _reportService = ReportService(_dataRepository);
    _loadJobData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  // Method to save notes with proper async gap handling
  Future<void> _saveNotes() async {
    try {
      await _supabaseService.updateJobNotes(
        widget.jobId,
        _notesController.text,
      );

      // Refresh job data
      await _loadJobData();

      if (!mounted) return;

      // Use centralized feedback system
      ErrorDisplay.showSuccess(context, 'Notes saved successfully');
    } catch (e) {
      if (!mounted) return;

      // Use centralized error handling
      final appError = AppError.fromException(
        e,
        context: {'operation': 'saveJobNotes', 'jobId': widget.jobId},
      );
      ErrorHandler.logError(appError);
      ErrorDisplay.showSnackBar(context, appError);
    }
  }

  // Method to update sync estimate items setting with proper async gap handling
  Future<void> _updateSyncEstimateItems(
    bool value,
    BuildContext contextToUse,
  ) async {
    try {
      await _supabaseService.updateJobSyncEstimateItems(widget.jobId, value);

      if (!mounted) return;

      setState(() {
        _job = _job!.copyWith(syncEstimateItems: value);
      });

      // Use centralized feedback system
      ErrorDisplay.showSuccess(
        context,
        'Estimate Line Item Sync ${value ? 'enabled' : 'disabled'}',
      );
    } catch (e) {
      if (!mounted) return;

      // Use centralized error handling
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'updateSyncEstimateItems',
          'jobId': widget.jobId,
          'value': value,
        },
      );
      ErrorHandler.logError(appError);
      ErrorDisplay.showSnackBar(context, appError);
    }
  }

  // Method to update live cost sync setting with proper async gap handling
  Future<void> _updateLiveCostSync(
    bool value,
    BuildContext contextToUse,
  ) async {
    try {
      await _supabaseService.updateJobLiveCostSync(widget.jobId, value);

      if (!mounted) return;

      setState(() {
        _job = _job!.copyWith(liveCostSyncEnabled: value);
      });

      // Use the current context since we've already checked mounted
      ErrorDisplay.showOperation(
        context,
        'Live Job Cost Sync ${value ? 'enabled' : 'disabled'}',
      );
    } catch (e) {
      if (!mounted) return;

      // Use the current context since we've already checked mounted
      ErrorDisplay.showWarning(
        context,
        'Failed to update setting: ${e.toString()}',
      );
    }
  }

  // Method to update sync expenses setting with proper async gap handling
  Future<void> _updateSyncExpenses(
    bool value,
    BuildContext contextToUse,
  ) async {
    try {
      await _supabaseService.updateJobSyncExpenses(widget.jobId, value);

      if (!mounted) return;

      setState(() {
        _job = _job!.copyWith(syncExpenses: value);
      });

      // Use centralized feedback system
      ErrorDisplay.showSuccess(
        context,
        'Expense Sync ${value ? 'enabled' : 'disabled'}',
      );
    } catch (e) {
      if (!mounted) return;

      // Use centralized error handling
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'updateSyncExpenses',
          'jobId': widget.jobId,
          'value': value,
        },
      );
      ErrorHandler.logError(appError);
      ErrorDisplay.showSnackBar(context, appError);
    }
  }

  // Method to update sync mileage setting with proper async gap handling
  Future<void> _updateSyncMileage(bool value, BuildContext contextToUse) async {
    try {
      await _supabaseService.updateJobSyncMileage(widget.jobId, value);

      if (!mounted) return;

      setState(() {
        _job = _job!.copyWith(syncMileage: value);
      });

      // Use centralized feedback system
      ErrorDisplay.showSuccess(
        context,
        'Mileage Sync ${value ? 'enabled' : 'disabled'}',
      );
    } catch (e) {
      if (!mounted) return;

      // Use centralized error handling
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'updateSyncMileage',
          'jobId': widget.jobId,
          'value': value,
        },
      );
      ErrorHandler.logError(appError);
      ErrorDisplay.showSnackBar(context, appError);
    }
  }

  // Method to update sync labor costs setting with proper async gap handling
  Future<void> _updateSyncLaborCosts(
    bool value,
    BuildContext contextToUse,
  ) async {
    try {
      await _supabaseService.updateJobSyncLaborCosts(widget.jobId, value);

      if (!mounted) return;

      setState(() {
        _job = _job!.copyWith(syncLaborCosts: value);
      });

      // Use centralized feedback system
      ErrorDisplay.showSuccess(
        context,
        'Hours & Labor Sync ${value ? 'enabled' : 'disabled'}',
      );
    } catch (e) {
      if (!mounted) return;

      // Use centralized error handling
      final appError = AppError.fromException(
        e,
        context: {
          'operation': 'updateSyncLaborCosts',
          'jobId': widget.jobId,
          'value': value,
        },
      );
      ErrorHandler.logError(appError);
      ErrorDisplay.showSnackBar(context, appError);
    }
  }

  Future<void> _loadJobData() async {
    if (!mounted) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadJobData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get job details
          final job = await _supabaseService.getJobById(widget.jobId);
          if (!mounted) return;

          // Get customer details
          final customer = await _supabaseService.getCustomerById(
            job.customerId,
          );
          if (!mounted) return;

          // Get related data
          final estimates = await _supabaseService.getEstimatesByJob(
            widget.jobId,
          );
          final expenses = await _supabaseService.getExpensesByJob(
            widget.jobId,
          );
          final timeLogs = await _supabaseService.getTimeLogsByJob(
            widget.jobId,
          );
          final invoices = await _supabaseService.getInvoicesByJob(
            widget.jobId,
          );
          final payments = await _supabaseService.getPaymentsByJob(
            widget.jobId,
          );
          if (!mounted) return;

          // Calculate financial metrics
          final totalExpenses = expenses.fold(0.0, (sum, e) => sum + e.amount);

          // Calculate labor costs from time logs
          final totalLaborCost = timeLogs.fold(
            0.0,
            (sum, t) => sum + t.laborCost,
          );

          // Calculate actual income from payments
          final totalActualIncome = payments.fold(
            0.0,
            (sum, p) => sum + p.amountReceived,
          );

          // Update job with calculated values
          final updatedJob = job.copyWith(
            actualExpenses: totalExpenses,
            actualLaborCost: totalLaborCost,
            actualIncome: totalActualIncome,
          );

          setState(() {
            _job = updatedJob;
            _customer = customer;
            _jobData = {
              'estimates': estimates,
              'expenses': expenses,
              'timeLogs': timeLogs,
              'invoices': invoices,
              'payments': payments,
            };
          });
        },
        message: 'Loading job data...',
        errorMessage: 'Failed to load job data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load job data: ${e.toString()}';
        });
      }
    }
  }

  // Build budget chart widget
  Widget _buildBudgetChart() {
    if (_job == null || _jobData == null) {
      return const SizedBox.shrink();
    }

    return FutureBuilder<BudgetData>(
      future: _getBudgetData(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: InlineLoadingIndicator(
                text: 'Loading budget data...',
                size: 20.0,
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Error loading budget data: ${snapshot.error}',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          );
        }

        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        return BudgetChart(budgetData: snapshot.data!);
      },
    );
  }

  // Get budget data using the budget service
  Future<BudgetData> _getBudgetData() async {
    final budgetService = BudgetService(_dataRepository);
    return await budgetService.getBudgetData(widget.jobId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_job?.title ?? 'Job Details'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => JobFormScreen(job: _job),
                ),
              ).then((_) => _loadJobData());
            },
          ),
        ],
      ),
      body: Consumer<LoadingStateProvider>(
        builder: (context, loadingProvider, child) {
          final isLoading = loadingProvider.isLoading('loadJobData');

          return isLoading
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading job data...',
                  size: 32.0,
                  showOfflineStatus: true,
                ),
              )
              : _errorMessage != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _loadJobData,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : _job == null
              ? const Center(child: Text('Job not found'))
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return ListView(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 8.0 : 16.0,
                    ),
                    children: [
                      // Job Overview Section (Expanded by default)
                      AdaptiveDetailSection(
                        title: 'Job Overview',
                        icon: Icons.work,
                        initiallyExpanded: true,
                        sectionKey: 'job_overview_${widget.jobId}',
                        alwaysExpandedInOffice: true,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  _job!.title,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Chip(
                                label: Text(_job!.status),
                                backgroundColor: _getStatusColor(_job!.status),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),

                          // Customer Information
                          Row(
                            children: [
                              Icon(
                                Icons.person,
                                color: Theme.of(context).colorScheme.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Customer: ${_customer?.name ?? 'Unknown'}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),

                          if (_job!.address != null &&
                              _job!.address!.isNotEmpty) ...[
                            const SizedBox(height: 12),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Service Address:\n${_buildFullAddress()}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],

                          if (_job!.description != null) ...[
                            const SizedBox(height: 12),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.description,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'Description:\n${_job!.description!}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],

                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Timeline and Financial Summary
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Start Date',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    _job!.startDate != null
                                        ? DateFormat(
                                          'MMM d, yyyy',
                                        ).format(_job!.startDate!)
                                        : 'Not started',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'End Date',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    _job!.endDate != null
                                        ? DateFormat(
                                          'MMM d, yyyy',
                                        ).format(_job!.endDate!)
                                        : 'Not completed',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'Estimated Price',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    _job!.estimatedPrice != null
                                        ? '\$${_job!.estimatedPrice!.toStringAsFixed(2)}'
                                        : 'N/A',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Profit/Loss',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    '\$${_job!.profitLoss.toStringAsFixed(2)}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color:
                                          _job!.profitLoss >= 0
                                              ? Colors.green
                                              : Colors.red,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),

                          // Budget vs Actuals Chart
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),
                          _buildBudgetChart(),
                        ],
                      ),

                      // Invoice Settings Section (Collapsed by default)
                      AdaptiveDetailSection(
                        title: 'Invoice Settings',
                        icon: Icons.settings,
                        initiallyExpanded: false,
                        sectionKey: 'invoice_settings_${widget.jobId}',
                        alwaysExpandedInOffice: false,
                        children: [
                          // Live Job Cost Sync toggle
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.sync,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Live Job Cost Sync',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _job!.liveCostSyncEnabled
                                          ? 'Expenses and labor costs will be suggested when creating new invoices for this job.'
                                          : 'Expenses and labor costs will NOT be automatically suggested in new invoices.',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Switch(
                                value: _job!.liveCostSyncEnabled,
                                onChanged: (value) {
                                  final currentContext = context;
                                  _updateLiveCostSync(value, currentContext);
                                },
                                activeColor:
                                    Theme.of(context).colorScheme.primary,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Hours & Labor Costs Sync toggle
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.timer,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Hours & Labor Sync',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _job!.syncLaborCosts
                                          ? 'Time logs will be suggested when creating new invoices for this job.'
                                          : 'Time logs will NOT be automatically suggested in new invoices.',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Switch(
                                value: _job!.syncLaborCosts,
                                onChanged: (value) {
                                  final currentContext = context;
                                  _updateSyncLaborCosts(value, currentContext);
                                },
                                activeColor:
                                    Theme.of(context).colorScheme.primary,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Mileage Sync toggle
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.directions_car,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Mileage Sync',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _job!.syncMileage
                                          ? 'Mileage entries will be suggested when creating new invoices for this job.'
                                          : 'Mileage entries will NOT be automatically suggested in new invoices.',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Switch(
                                value: _job!.syncMileage,
                                onChanged: (value) {
                                  final currentContext = context;
                                  _updateSyncMileage(value, currentContext);
                                },
                                activeColor:
                                    Theme.of(context).colorScheme.primary,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Other Expenses Sync toggle
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.receipt_long,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Other Expenses Sync',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _job!.syncExpenses
                                          ? 'Other expenses will be suggested when creating new invoices for this job.'
                                          : 'Other expenses will NOT be automatically suggested in new invoices.',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Switch(
                                value: _job!.syncExpenses,
                                onChanged: (value) {
                                  final currentContext = context;
                                  _updateSyncExpenses(value, currentContext);
                                },
                                activeColor:
                                    Theme.of(context).colorScheme.primary,
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 16),

                          // Estimate Line Item Sync toggle
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.description,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        const Text(
                                          'Estimate Line Item Sync',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      _job!.syncEstimateItems
                                          ? 'Line items from approved estimates will be suggested when creating new invoices for this job.'
                                          : 'Line items from approved estimates will NOT be automatically suggested in new invoices.',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Switch(
                                value: _job!.syncEstimateItems,
                                onChanged: (value) {
                                  final currentContext = context;
                                  _updateSyncEstimateItems(
                                    value,
                                    currentContext,
                                  );
                                },
                                activeColor:
                                    Theme.of(context).colorScheme.primary,
                              ),
                            ],
                          ),
                        ],
                      ),

                      // Financial Summary Section (Collapsed by default)
                      AdaptiveDetailSection(
                        title: 'Financial Summary',
                        icon: Icons.analytics,
                        initiallyExpanded: false,
                        sectionKey: 'financial_summary_${widget.jobId}',
                        alwaysExpandedInOffice: displayProvider.isOfficeMode,
                        children: [_buildFinancialSummaryTab()],
                      ),

                      // Estimates Section (Collapsed by default)
                      AdaptiveDetailSection(
                        title: 'Estimates',
                        icon: Icons.description,
                        initiallyExpanded: false,
                        sectionKey: 'estimates_${widget.jobId}',
                        alwaysExpandedInOffice: false,
                        children: [_buildEstimatesTab()],
                      ),

                      // Expenses Section (Collapsed by default)
                      AdaptiveDetailSection(
                        title: 'Expenses',
                        icon: Icons.receipt_long,
                        initiallyExpanded: false,
                        sectionKey: 'expenses_${widget.jobId}',
                        alwaysExpandedInOffice: false,
                        children: [_buildExpensesTab()],
                      ),

                      // Time Logs Section (Collapsed by default)
                      AdaptiveDetailSection(
                        title: 'Time Logs',
                        icon: Icons.timer,
                        initiallyExpanded: false,
                        sectionKey: 'time_logs_${widget.jobId}',
                        alwaysExpandedInOffice: false,
                        children: [_buildTimeLogsTab()],
                      ),

                      // Invoices Section (Collapsed by default)
                      AdaptiveDetailSection(
                        title: 'Invoices',
                        icon: Icons.receipt,
                        initiallyExpanded: false,
                        sectionKey: 'invoices_${widget.jobId}',
                        alwaysExpandedInOffice: false,
                        children: [_buildInvoicesTab()],
                      ),

                      // Payments Section (Collapsed by default)
                      AdaptiveDetailSection(
                        title: 'Payments',
                        icon: Icons.payment,
                        initiallyExpanded: false,
                        sectionKey: 'payments_${widget.jobId}',
                        alwaysExpandedInOffice: false,
                        children: [_buildPaymentsTab()],
                      ),

                      // Notes Section (Collapsed by default)
                      AdaptiveDetailSection(
                        title: 'Notes',
                        icon: Icons.note,
                        initiallyExpanded: false,
                        sectionKey: 'notes_${widget.jobId}',
                        alwaysExpandedInOffice: false,
                        children: [_buildNotesTab()],
                      ),

                      const SizedBox(height: 32),
                    ],
                  );
                },
              );
        },
      ),
      floatingActionButton:
          _job != null
              ? Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return FloatingActionButton.extended(
                    onPressed: _isGeneratingReport ? null : _generateJobReport,
                    icon:
                        _isGeneratingReport
                            ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Theme.of(context).colorScheme.onPrimary,
                                ),
                              ),
                            )
                            : const Icon(Icons.assessment),
                    label: Text(
                      _isGeneratingReport ? 'Generating...' : 'Job Report',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 14 : 16,
                      ),
                    ),
                    backgroundColor: Theme.of(context).colorScheme.secondary,
                    foregroundColor: Colors.white,
                  );
                },
              )
              : null,
    );
  }

  Future<void> _generateJobReport() async {
    if (_job == null) return;

    setState(() {
      _isGeneratingReport = true;
    });

    try {
      // Create filters for the job report (all time for this specific job)
      final filters = report_models.ReportFilters(
        timePeriod:
            report_models
                .TimePeriod
                .currentYear, // Will be overridden by custom range
        customDateRange: report_models.DateRange(
          startDate: DateTime(2020, 1, 1), // Start from a very early date
          endDate: DateTime.now().add(const Duration(days: 1)), // Include today
        ),
        selectedJobIds: [widget.jobId],
        includeAllJobs: false,
      );

      // Generate the job-specific report
      final reportData = await _reportService.generateJobReport(
        jobId: widget.jobId,
        filters: filters,
      );

      // Generate PDF
      final pdfBytes = await _reportService.generateReportPdf(reportData);
      final filePath = await _reportService.savePdfToFile(
        pdfBytes,
        reportData.fileName,
      );

      // Share the PDF
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(filePath)],
          text: 'Job Report: ${_job!.title}',
          subject: 'Job Report - ${_job!.title}',
        ),
      );

      if (mounted) {
        // Use centralized feedback system
        ErrorDisplay.showOperation(context, FeedbackMessages.pdfGenerated);
      }
    } catch (e) {
      if (mounted) {
        // Use centralized error handling
        final appError = AppError.fromException(
          e,
          context: {'operation': 'generateJobReport', 'jobId': widget.jobId},
        );
        ErrorHandler.logError(appError);
        ErrorDisplay.showSnackBar(context, appError);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingReport = false;
        });
      }
    }
  }

  Widget _buildEstimatesTab() {
    final jobData = _jobData;
    if (jobData == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final estimates = jobData['estimates'] as List<Estimate>? ?? [];

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Create New Estimate'),
            onPressed: () {
              Navigator.of(context)
                  .push(
                    MaterialPageRoute(
                      builder:
                          (context) => EstimateFormScreen(jobId: widget.jobId),
                    ),
                  )
                  .then((_) => _loadJobData());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        Expanded(
          child:
              estimates.isEmpty
                  ? const Center(child: Text('No estimates found'))
                  : ListView.builder(
                    itemCount: estimates.length,
                    itemBuilder: (context, index) {
                      final estimate = estimates[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          title: Text('Estimate #${index + 1}'),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Amount: \$${estimate.totalAmount.toStringAsFixed(2)}',
                              ),
                              Text('Status: ${estimate.status}'),
                            ],
                          ),
                          trailing: Chip(
                            label: Text(estimate.status),
                            backgroundColor: _getStatusColor(estimate.status),
                          ),
                          onTap: () {
                            Navigator.of(context)
                                .push(
                                  MaterialPageRoute(
                                    builder:
                                        (context) => EstimateDetailScreen(
                                          estimateId: estimate.id,
                                        ),
                                  ),
                                )
                                .then((_) => _loadJobData());
                          },
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildExpensesTab() {
    final expenses = _jobData?['expenses'] as List<Expense>? ?? [];
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Add New Expense'),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ExpenseFormScreen(jobId: widget.jobId),
                ),
              ).then((_) => _loadJobData());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        Expanded(
          child:
              expenses.isEmpty
                  ? const Center(child: Text('No expenses found'))
                  : ListView.builder(
                    itemCount: expenses.length,
                    itemBuilder: (context, index) {
                      final expense = expenses[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          title: Text(expense.description),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Amount: \$${expense.amount.toStringAsFixed(2)}',
                              ),
                              Text(
                                'Category: ${expense.category ?? 'Uncategorized'}',
                              ),
                            ],
                          ),
                          trailing: Chip(
                            label: Text(expense.category ?? 'Uncategorized'),
                            backgroundColor: _getCategoryColor(
                              expense.category,
                            ),
                          ),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => ExpenseDetailScreen(
                                      expenseId: expense.id,
                                    ),
                              ),
                            ).then((_) => _loadJobData());
                          },
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildTimeLogsTab() {
    final timeLogs = _jobData?['timeLogs'] as List<TimeLog>? ?? [];
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Add New Time Log'),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => TimeLogFormScreen(jobId: widget.jobId),
                ),
              ).then((_) => _loadJobData());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        Expanded(
          child:
              timeLogs.isEmpty
                  ? const Center(child: Text('No time logs found'))
                  : ListView.builder(
                    itemCount: timeLogs.length,
                    itemBuilder: (context, index) {
                      final timeLog = timeLogs[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          title: Text(
                            timeLog.notes ?? 'Time Log #${index + 1}',
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Hours: ${timeLog.hours.toStringAsFixed(1)}',
                              ),
                              Text(
                                'Cost: \$${timeLog.laborCost.toStringAsFixed(2)}',
                              ),
                            ],
                          ),
                          trailing: Text(
                            '${timeLog.date.month}/${timeLog.date.day}/${timeLog.date.year}',
                          ),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) =>
                                        TimeLogDetailScreen(timeLog: timeLog),
                              ),
                            ).then((_) => _loadJobData());
                          },
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildInvoicesTab() {
    final invoices = _jobData?['invoices'] as List<Invoice>? ?? [];
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ElevatedButton.icon(
                icon: const Icon(Icons.add),
                label: const Text('Create New Invoice'),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) => InvoiceFormScreen(jobId: widget.jobId),
                    ),
                  ).then((_) => _loadJobData());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              // Live Job Cost Sync status indicator for invoices tab
              if (_job != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        _job!.liveCostSyncEnabled
                            ? Colors.blue.withAlpha(76) // 0.3 * 255 = 76
                            : Colors.grey.withAlpha(76), // 0.3 * 255 = 76
                    borderRadius: BorderRadius.circular(8),
                    border:
                        _job!.liveCostSyncEnabled
                            ? Border.all(
                              color: Colors.blue.withAlpha(128),
                            ) // 0.5 * 255 = 128
                            : null,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        _job!.liveCostSyncEnabled
                            ? Icons.sync
                            : Icons.sync_disabled,
                        color:
                            _job!.liveCostSyncEnabled
                                ? Colors.blue
                                : Colors.grey.shade600,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _job!.liveCostSyncEnabled
                                  ? 'Live Job Cost Sync is enabled'
                                  : 'Live Job Cost Sync is disabled',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color:
                                    _job!.liveCostSyncEnabled
                                        ? Colors.blue
                                        : Colors.grey.shade600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Configure which items should be available for inclusion in invoices for this job. '
                              'Time logs track hours worked, and mileage is primarily for tax purposes. '
                              'Overhead expenses are never included in invoices.',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
        Expanded(
          child:
              invoices.isEmpty
                  ? const Center(child: Text('No invoices found'))
                  : ListView.builder(
                    itemCount: invoices.length,
                    itemBuilder: (context, index) {
                      final invoice = invoices[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          title: Text(
                            'Invoice #${invoice.id != null && invoice.id!.isNotEmpty ? (invoice.id!.length > 8 ? invoice.id!.substring(0, 8) : invoice.id!) : "Unknown"}',
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Amount: \$${invoice.totalAmount.toStringAsFixed(2)}',
                              ),
                              Text('Status: ${invoice.status}'),
                            ],
                          ),
                          trailing: Chip(
                            label: Text(invoice.status),
                            backgroundColor: _getStatusColor(invoice.status),
                          ),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => InvoiceDetailScreen(
                                      invoiceId: invoice.id ?? '',
                                    ),
                              ),
                            ).then((_) => _loadJobData());
                          },
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildPaymentsTab() {
    final payments = _jobData?['payments'] as List<Payment>? ?? [];
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Add New Payment'),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PaymentFormScreen(jobId: widget.jobId),
                ),
              ).then((_) => _loadJobData());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        Expanded(
          child:
              payments.isEmpty
                  ? const Center(child: Text('No payments found'))
                  : ListView.builder(
                    itemCount: payments.length,
                    itemBuilder: (context, index) {
                      final payment = payments[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        child: ListTile(
                          title: Text(
                            '\$${payment.amountReceived.toStringAsFixed(2)}',
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Date: ${DateFormat('MM/dd/yyyy').format(payment.paymentDate)}',
                              ),
                              Text('Method: ${payment.paymentMethod}'),
                              if (payment.invoiceId != null &&
                                  payment.invoiceId!.isNotEmpty)
                                Text(
                                  'Linked to Invoice #${payment.invoiceId!.length >= 8 ? payment.invoiceId!.substring(0, 8) : payment.invoiceId!}',
                                ),
                            ],
                          ),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => PaymentDetailScreen(
                                      paymentId: payment.id,
                                    ),
                              ),
                            ).then((_) => _loadJobData());
                          },
                        ),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildNotesTab() {
    final job = _job; // Create a local reference to _job
    if (job == null) {
      return const Center(child: Text('Job not found'));
    }

    // Use class-level controller instead of local one
    if (_notesController.text.isEmpty) {
      _notesController.text = job.description ?? '';
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Job Notes',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              IconButton(
                icon: Icon(_isEditing ? Icons.save : Icons.edit),
                onPressed: () {
                  if (_isEditing) {
                    // Save the notes
                    _saveNotes();
                  }

                  setState(() {
                    _isEditing = !_isEditing;
                  });
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child:
                _isEditing
                    ? TextField(
                      controller: _notesController,
                      maxLines: null,
                      expands: true,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Enter notes about this job...',
                      ),
                    )
                    : SingleChildScrollView(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          job.description?.isNotEmpty == true
                              ? job.description!
                              : 'No notes available for this job.',
                          style: TextStyle(
                            color:
                                job.description?.isNotEmpty == true
                                    ? Colors.black
                                    : Colors.grey,
                          ),
                        ),
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'estimate':
        return Colors.blue[100]!;
      case 'active':
        return Colors.green[100]!;
      case 'completed':
        return Colors.purple[100]!;
      case 'invoiced':
        return Colors.orange[100]!;
      case 'paid':
        return Colors.green[300]!;
      default:
        return Colors.grey[300]!;
    }
  }

  Color _getCategoryColor(String? category) {
    if (category == null) return Colors.grey[300]!;

    // Use different colors for different categories
    switch (category) {
      case ExpenseCategory.advertising:
        return Colors.blue[100]!;
      case ExpenseCategory.carAndTruck:
        return Colors.green[100]!;
      case ExpenseCategory.contractLabor:
        return Colors.orange[100]!;
      case ExpenseCategory.officeExpense:
        return Colors.purple[100]!;
      case ExpenseCategory.legalAndProfessional:
        return Colors.red[100]!;
      default:
        return Colors.grey[300]!;
    }
  }

  // Build full address from job address components
  String _buildFullAddress() {
    if (_job == null) return 'Not provided';

    final parts =
        [
          _job!.address,
          _job!.city,
          _job!.state,
          _job!.zipCode,
        ].where((part) => part != null && part.isNotEmpty).toList();

    return parts.isEmpty ? 'Not provided' : parts.join(', ');
  }

  Widget _buildFinancialSummaryTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Financial Summary',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Income: \$0.00'),
                  const SizedBox(height: 8),
                  const Text('Expenses: \$0.00'),
                  const SizedBox(height: 8),
                  const Text('Labor: \$0.00'),
                  const SizedBox(height: 8),
                  const Text('Profit/Loss: \$0.00'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
