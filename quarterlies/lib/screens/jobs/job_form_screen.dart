import 'package:flutter/material.dart';
import 'dart:async';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/widgets/address_autocomplete_field.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/adaptive_form_section.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:quarterlies/services/voice_recording_service.dart';
import 'package:intl/intl.dart';

class JobFormScreen extends StatefulWidget {
  final Job? job; // Null for new job, non-null for editing

  const JobFormScreen({super.key, this.job});

  @override
  State<JobFormScreen> createState() => _JobFormScreenState();
}

class _JobFormScreenState extends State<JobFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _estimatedPriceController = TextEditingController();
  final _estimatedBudgetController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipCodeController = TextEditingController();
  final _dataRepository = DataRepository();
  final _voiceRecordingService = VoiceRecordingService();

  DateTime? _startDate;
  DateTime? _endDate;
  String _status = 'estimate';
  String? _selectedCustomerId;
  List<Customer> _customers = [];
  Customer? _selectedCustomer;
  bool _useCustomerAddress = false;

  // Job-specific settings (will inherit from user settings for new jobs)
  bool _syncExpenses = false;
  bool _syncMileage = false;
  bool _syncLaborCosts = false;
  bool _syncEstimateItems = true;
  bool _summarizeMileage = true;
  bool _summarizeHours = false;
  int _defaultInvoiceDueDays = 30;

  bool _isLoadingCustomers = true;
  bool _isRecording = false;
  bool _isOffline = false;
  String? _errorMessage;

  // Stream subscription for connectivity changes
  StreamSubscription<bool>? _connectivitySubscription;

  bool get _isEditing => widget.job != null;

  @override
  void initState() {
    super.initState();
    _setupConnectivityListener();
    _loadCustomers();
    _loadUserSettings();

    if (_isEditing) {
      // Populate form fields with existing job data
      _titleController.text = widget.job!.title;
      _descriptionController.text = widget.job!.description ?? '';
      _estimatedPriceController.text =
          widget.job!.estimatedPrice?.toString() ?? '';
      _estimatedBudgetController.text =
          widget.job!.estimatedExpensesBudget?.toString() ?? '';
      _addressController.text = widget.job!.address ?? '';
      _cityController.text = widget.job!.city ?? '';
      _stateController.text = widget.job!.state ?? '';
      _zipCodeController.text = widget.job!.zipCode ?? '';
      _startDate = widget.job!.startDate;
      _endDate = widget.job!.endDate;
      _status = widget.job!.status;
      _selectedCustomerId = widget.job!.customerId;

      // Load existing job-specific settings
      _syncExpenses = widget.job!.syncExpenses;
      _syncMileage = widget.job!.syncMileage;
      _syncLaborCosts = widget.job!.syncLaborCosts;
      _syncEstimateItems = widget.job!.syncEstimateItems;
      _summarizeMileage = widget.job!.summarizeMileage;
      _summarizeHours = widget.job!.summarizeHours;
      _defaultInvoiceDueDays = widget.job!.defaultInvoiceDueDays ?? 30;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _estimatedPriceController.dispose();
    _estimatedBudgetController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _connectivitySubscription?.cancel();
    super.dispose();
  }

  // Set up listener for connectivity changes
  Future<void> _setupConnectivityListener() async {
    // Check initial connectivity status
    _isOffline = !(await _dataRepository.isOnline());

    // Listen for connectivity changes
    _connectivitySubscription = _dataRepository.connectionStatus.listen((
      isConnected,
    ) {
      if (mounted) {
        setState(() {
          _isOffline = !isConnected;
        });

        // Show sync feedback when connectivity status changes
        if (isConnected) {
          ErrorDisplay.showSync(
            context,
            'Back online. Data will sync automatically.',
          );
        } else {
          ErrorDisplay.showSync(
            context,
            'Working offline. Changes saved locally.',
            isOffline: true,
          );
        }
      }
    });
  }

  Future<void> _loadCustomers() async {
    try {
      final customers = await _dataRepository.getCustomers();
      setState(() {
        _customers = customers;
        _isLoadingCustomers = false;
      });

      // If editing, find the selected customer
      if (_isEditing && _selectedCustomerId != null) {
        _updateSelectedCustomer(_selectedCustomerId!);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load customers: ${e.toString()}';
        _isLoadingCustomers = false;
      });
    }
  }

  Future<void> _loadUserSettings() async {
    try {
      final userSettings = await _dataRepository.getUserSettings();
      setState(() {
        // For new jobs, apply user settings as defaults
        if (!_isEditing) {
          _syncExpenses = userSettings.syncExpenses;
          _syncMileage = userSettings.syncMileage;
          _syncLaborCosts = userSettings.syncLaborCosts;
          _syncEstimateItems = userSettings.syncEstimateItems;
          _summarizeMileage = userSettings.showMileageAsSummary;
          _summarizeHours =
              !userSettings.showHoursAsIndividual; // Inverted logic
          _defaultInvoiceDueDays = userSettings.defaultInvoiceDueDays;
        }
      });
    } catch (e) {
      // If we can't load user settings, use defaults
      debugPrint('Failed to load user settings: ${e.toString()}');
    }
  }

  // Update the selected customer when customer ID changes
  void _updateSelectedCustomer(String customerId) {
    final customer = _customers.firstWhere(
      (c) => c.id == customerId,
      orElse: () => Customer(userId: '', name: ''),
    );

    setState(() {
      _selectedCustomer = customer;

      // If using customer address, update the address fields
      if (_useCustomerAddress) {
        _updateAddressFromCustomer();
      }
    });
  }

  // Update address fields from the selected customer
  void _updateAddressFromCustomer() {
    if (_selectedCustomer != null) {
      _addressController.text = _selectedCustomer!.address ?? '';
      _cityController.text = _selectedCustomer!.city ?? '';
      _stateController.text = _selectedCustomer!.state ?? '';
      _zipCodeController.text = _selectedCustomer!.zipCode ?? '';
    }
  }

  Future<void> _saveJob() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCustomerId == null) {
      setState(() {
        _errorMessage = 'Please select a customer';
      });
      return;
    }

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'saveJob',
        () async {
          setState(() {
            _errorMessage = null;
          });

          final userId = Supabase.instance.client.auth.currentUser!.id;
          double? estimatedPrice;
          if (_estimatedPriceController.text.isNotEmpty) {
            estimatedPrice = double.tryParse(_estimatedPriceController.text);
          }

          double? estimatedBudget;
          if (_estimatedBudgetController.text.isNotEmpty) {
            estimatedBudget = double.tryParse(_estimatedBudgetController.text);
          }

          if (_isEditing) {
            // Update existing job
            final updatedJob = Job(
              id: widget.job!.id,
              userId: userId,
              customerId: _selectedCustomerId!,
              title: _titleController.text.trim(),
              description:
                  _descriptionController.text.trim().isNotEmpty
                      ? _descriptionController.text.trim()
                      : null,
              address:
                  _addressController.text.trim().isNotEmpty
                      ? _addressController.text.trim()
                      : null,
              city:
                  _cityController.text.trim().isNotEmpty
                      ? _cityController.text.trim()
                      : null,
              state:
                  _stateController.text.trim().isNotEmpty
                      ? _stateController.text.trim()
                      : null,
              zipCode:
                  _zipCodeController.text.trim().isNotEmpty
                      ? _zipCodeController.text.trim()
                      : null,
              estimatedPrice: estimatedPrice,
              estimatedExpensesBudget: estimatedBudget,
              status: _status,
              startDate: _startDate,
              endDate: _endDate,
              actualIncome: widget.job!.actualIncome,
              actualExpenses: widget.job!.actualExpenses,
              actualLaborCost: widget.job!.actualLaborCost,
              // Job-specific settings
              syncExpenses: _syncExpenses,
              syncMileage: _syncMileage,
              syncLaborCosts: _syncLaborCosts,
              syncEstimateItems: _syncEstimateItems,
              summarizeMileage: _summarizeMileage,
              summarizeHours: _summarizeHours,
              defaultInvoiceDueDays: _defaultInvoiceDueDays,
              createdAt: widget.job!.createdAt,
              updatedAt: DateTime.now(),
            );

            await _dataRepository.addJob(updatedJob);
          } else {
            // Create new job
            final newJob = Job(
              userId: userId,
              customerId: _selectedCustomerId!,
              title: _titleController.text.trim(),
              description:
                  _descriptionController.text.trim().isNotEmpty
                      ? _descriptionController.text.trim()
                      : null,
              address:
                  _addressController.text.trim().isNotEmpty
                      ? _addressController.text.trim()
                      : null,
              city:
                  _cityController.text.trim().isNotEmpty
                      ? _cityController.text.trim()
                      : null,
              state:
                  _stateController.text.trim().isNotEmpty
                      ? _stateController.text.trim()
                      : null,
              zipCode:
                  _zipCodeController.text.trim().isNotEmpty
                      ? _zipCodeController.text.trim()
                      : null,
              estimatedPrice: estimatedPrice,
              estimatedExpensesBudget: estimatedBudget,
              status: _status,
              startDate: _startDate,
              endDate: _endDate,
              // Job-specific settings (inherited from user settings)
              syncExpenses: _syncExpenses,
              syncMileage: _syncMileage,
              syncLaborCosts: _syncLaborCosts,
              syncEstimateItems: _syncEstimateItems,
              summarizeMileage: _summarizeMileage,
              summarizeHours: _summarizeHours,
              defaultInvoiceDueDays: _defaultInvoiceDueDays,
            );

            await _dataRepository.addJob(newJob);
          }

          if (mounted) {
            // Show success feedback
            final operation = _isEditing ? 'update' : 'save';
            ErrorDisplay.showDataOperation(
              context,
              'job',
              operation,
              isOffline: _isOffline,
            );
            Navigator.pop(context, true); // Return true to indicate success
          }
        },
        message: _isEditing ? 'Updating job...' : 'Creating job...',
        errorMessage: 'Failed to save job',
      );
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {
            'operation': 'saveJob',
            'isEditing': _isEditing,
            'jobTitle': _titleController.text.trim(),
            'customerId': _selectedCustomerId,
          },
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        // Show error dialog for critical errors
        if (appError.severity == ErrorSeverity.high ||
            appError.severity == ErrorSeverity.critical) {
          ErrorDisplay.showErrorDialog(
            context,
            appError,
            onRetry: () => _saveJob(),
          );
        } else {
          // Show snackbar for less critical errors
          ErrorDisplay.showSnackBar(
            context,
            appError,
            onRetry: () => _saveJob(),
          );
        }
      }
    }
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isStartDate
              ? (_startDate ?? DateTime.now())
              : (_endDate ?? DateTime.now()),
      firstDate: isStartDate ? DateTime(2000) : (_startDate ?? DateTime(2000)),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  // Voice recording methods
  Future<void> _startRecording() async {
    try {
      await _voiceRecordingService.initialize();
      await _voiceRecordingService.startRecording();
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'startRecording'},
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      final transcribedText = await _voiceRecordingService.stopRecording();
      setState(() {
        _isRecording = false;
      });

      if (transcribedText.isNotEmpty) {
        // Process the transcribed text specifically for job information
        final extractedInfo = _voiceRecordingService.processJobVoiceInput(
          transcribedText,
        );

        setState(() {
          // Apply extracted information to form fields
          if (extractedInfo.containsKey('title')) {
            _titleController.text = extractedInfo['title'];
          }
          if (extractedInfo.containsKey('description')) {
            _descriptionController.text = extractedInfo['description'];
          }
          if (extractedInfo.containsKey('estimatedPrice')) {
            _estimatedPriceController.text = extractedInfo['estimatedPrice'];
          }
          if (extractedInfo.containsKey('status')) {
            _status = extractedInfo['status'];
          }

          // Apply address information if available
          if (extractedInfo.containsKey('address')) {
            _addressController.text = extractedInfo['address'];
            // Set useCustomerAddress to false since we're using a specific address
            _useCustomerAddress = false;
          }
          if (extractedInfo.containsKey('city')) {
            _cityController.text = extractedInfo['city'];
          }
          if (extractedInfo.containsKey('state')) {
            _stateController.text = extractedInfo['state'];
          }
          if (extractedInfo.containsKey('zipCode')) {
            _zipCodeController.text = extractedInfo['zipCode'];
          }

          // If a date was mentioned, use it as the start date
          if (extractedInfo.containsKey('date')) {
            try {
              final dateStr = extractedInfo['date'];
              final date = DateFormat('yyyy-MM-dd').parse(dateStr);
              _startDate = date;
            } catch (e) {
              // Ignore date parsing errors
            }
          }
        });

        // Show voice operation feedback
        if (mounted) {
          ErrorDisplay.showOperation(
            context,
            'Voice recording processed successfully',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        final appError = AppError.fromException(
          e,
          context: {'operation': 'stopRecording'},
        );
        ErrorHandler.logError(appError);

        setState(() {
          _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          _isRecording = false;
        });

        ErrorDisplay.showSnackBar(context, appError);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Job' : 'New Job'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          // Voice recording button in app bar
          IconButton(
            icon:
                _isRecording
                    ? const Icon(Icons.stop_circle, color: Colors.red)
                    : const Icon(Icons.mic),
            onPressed: _isRecording ? _stopRecording : _startRecording,
            tooltip: _isRecording ? 'Stop recording' : 'Add job using voice',
          ),
        ],
      ),
      body:
          _isLoadingCustomers
              ? const Center(
                child: QuarterliesLoadingIndicator(
                  message: 'Loading customers...',
                  size: 32.0,
                ),
              )
              : Consumer<DisplaySettingsProvider>(
                builder: (context, displayProvider, child) {
                  return SingleChildScrollView(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 12.0 : 16.0,
                    ),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          if (_errorMessage != null)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16.0),
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade50,
                                  border: Border.all(
                                    color: Colors.red.shade200,
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.error_outline,
                                      color: Colors.red.shade700,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        _errorMessage!,
                                        style: TextStyle(
                                          color: Colors.red.shade700,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.close),
                                      iconSize: 18,
                                      color: Colors.red.shade700,
                                      onPressed: () {
                                        setState(() {
                                          _errorMessage = null;
                                        });
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          // Basic Job Information Section
                          AdaptiveFormSection(
                            title: 'Basic Job Information',
                            icon: Icons.work,
                            children: [
                              // Customer dropdown
                              DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  labelText: 'Customer',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  filled: true,
                                  fillColor: Colors.grey[100],
                                ),
                                value: _selectedCustomerId,
                                items:
                                    _customers.map((customer) {
                                      return DropdownMenuItem<String>(
                                        value: customer.id,
                                        child: Text(customer.name),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _selectedCustomerId = value;
                                    });
                                    _updateSelectedCustomer(value);
                                  }
                                },
                                validator:
                                    (value) => InputValidators.validateRequired(
                                      value,
                                      'Customer',
                                    ),
                              ),

                              // Job title with voice indicator
                              Row(
                                children: [
                                  Expanded(
                                    child: CustomTextField(
                                      controller: _titleController,
                                      labelText: 'Job Title',
                                      hintText: 'Enter job title',
                                      validator:
                                          (value) =>
                                              InputValidators.validateRequired(
                                                value,
                                                'Job title',
                                              ),
                                    ),
                                  ),
                                  if (_isRecording)
                                    Container(
                                      margin: const EdgeInsets.only(left: 8),
                                      child: const VoiceLoadingIndicator(
                                        operation: 'Recording...',
                                        isRecording: true,
                                      ),
                                    ),
                                ],
                              ),

                              if (displayProvider.isOfficeMode) ...[
                                // Office Mode: Estimated price and budget in same row
                                Row(
                                  children: [
                                    Expanded(
                                      child: CustomTextField(
                                        controller: _estimatedPriceController,
                                        labelText: 'Estimated Price',
                                        hintText:
                                            'Enter estimated price (optional)',
                                        keyboardType: TextInputType.number,
                                        validator:
                                            (value) =>
                                                InputValidators.validateCurrency(
                                                  value,
                                                  required: false,
                                                  fieldName: 'Estimated price',
                                                ),
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: CustomTextField(
                                        controller: _estimatedBudgetController,
                                        labelText: 'Estimated Expenses Budget',
                                        hintText:
                                            'Enter estimated expenses budget (optional)',
                                        keyboardType: TextInputType.number,
                                        validator:
                                            (
                                              value,
                                            ) => InputValidators.validateCurrency(
                                              value,
                                              required: false,
                                              fieldName:
                                                  'Estimated expenses budget',
                                            ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                          // Job description with voice recording button
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 12.0),
                            child: Stack(
                              alignment: Alignment.centerRight,
                              children: [
                                TextFormField(
                                  controller: _descriptionController,
                                  decoration: InputDecoration(
                                    labelText: 'Description',
                                    labelStyle: TextStyle(
                                      fontSize: 16,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                    hintText:
                                        'Enter job description or tap mic to use voice',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 18,
                                    ),
                                    suffixIcon:
                                        _isRecording
                                            ? Container(
                                              margin: const EdgeInsets.only(
                                                right: 32,
                                              ),
                                              child: const Icon(
                                                Icons.mic,
                                                color: Colors.red,
                                              ),
                                            )
                                            : null,
                                  ),
                                  keyboardType: TextInputType.multiline,
                                  maxLines: 3,
                                ),
                                Positioned(
                                  right: 8,
                                  child: IconButton(
                                    icon: Icon(
                                      _isRecording ? Icons.stop : Icons.mic,
                                      color:
                                          _isRecording
                                              ? Colors.red
                                              : Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                    ),
                                    onPressed:
                                        _isRecording
                                            ? _stopRecording
                                            : _startRecording,
                                    tooltip:
                                        _isRecording
                                            ? 'Stop recording'
                                            : 'Record job details',
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Estimated price
                          CustomTextField(
                            controller: _estimatedPriceController,
                            labelText: 'Estimated Price',
                            hintText: 'Enter estimated price (optional)',
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                final price = double.tryParse(value);
                                if (price == null) {
                                  return 'Please enter a valid number';
                                }
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          // Estimated expenses budget
                          CustomTextField(
                            controller: _estimatedBudgetController,
                            labelText: 'Estimated Expenses Budget',
                            hintText:
                                'Enter estimated expenses budget (optional)',
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                final budget = double.tryParse(value);
                                if (budget == null) {
                                  return 'Please enter a valid number';
                                }
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Service Address Section
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.blue.shade200),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.location_on,
                                      color: Colors.blue,
                                    ),
                                    const SizedBox(width: 8),
                                    const Text(
                                      'Service Address',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Tooltip(
                                      message:
                                          'This is the location where the work will be performed, which may be different from the customer\'s billing address. This address will appear on all documents as "Service Address".',
                                      child: const Icon(
                                        Icons.info_outline,
                                        size: 16,
                                        color: Colors.blue,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'The service address is where the work will be performed. It will appear on all estimates, contracts, and invoices.',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.black54,
                                  ),
                                ),
                                const SizedBox(height: 16),
                              ],
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Use customer address checkbox
                          if (_selectedCustomer != null)
                            CheckboxListTile(
                              title: Row(
                                children: [
                                  const Text('Use Customer Billing Address'),
                                  const SizedBox(width: 4),
                                  Tooltip(
                                    message:
                                        'Check this if the service location is the same as the customer\'s billing address.',
                                    child: const Icon(
                                      Icons.help_outline,
                                      size: 14,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                              value: _useCustomerAddress,
                              onChanged: (value) {
                                setState(() {
                                  _useCustomerAddress = value ?? false;
                                  if (_useCustomerAddress) {
                                    _updateAddressFromCustomer();
                                  }
                                });
                              },
                              controlAffinity: ListTileControlAffinity.leading,
                              contentPadding: EdgeInsets.zero,
                            ),
                          const SizedBox(height: 8),

                          // Address fields
                          AddressAutocompleteField(
                            controller: _addressController,
                            labelText: 'Service Location Street Address',
                            hintText:
                                'Enter the address where work will be performed',
                            onAddressSelected: (address) {
                              // Update city, state, and zip code fields
                              _cityController.text = address.city;
                              _stateController.text = address.state;
                              _zipCodeController.text = address.zipCode;
                            },
                          ),
                          const SizedBox(height: 8),

                          // City, State, Zip
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: CustomTextField(
                                  controller: _cityController,
                                  labelText: 'Service Location City',
                                  hintText: 'Enter city',
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: CustomTextField(
                                  controller: _stateController,
                                  labelText: 'State',
                                  hintText: 'State',
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          CustomTextField(
                            controller: _zipCodeController,
                            labelText: 'Service Location ZIP Code',
                            hintText: 'Enter ZIP code',
                            keyboardType: TextInputType.number,
                          ),
                          const SizedBox(height: 16),
                          // Status dropdown
                          DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              labelText: 'Status',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              filled: true,
                              fillColor: Colors.grey[100],
                            ),
                            value: _status,
                            items: const [
                              DropdownMenuItem(
                                value: 'estimate',
                                child: Text('Estimate'),
                              ),
                              DropdownMenuItem(
                                value: 'active',
                                child: Text('Active'),
                              ),
                              DropdownMenuItem(
                                value: 'completed',
                                child: Text('Completed'),
                              ),
                              DropdownMenuItem(
                                value: 'invoiced',
                                child: Text('Invoiced'),
                              ),
                              DropdownMenuItem(
                                value: 'paid',
                                child: Text('Paid'),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _status = value!;
                              });
                            },
                          ),
                          const SizedBox(height: 16),
                          // Date pickers
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () => _selectDate(context, true),
                                  icon: const Icon(Icons.calendar_today),
                                  label: Text(
                                    _startDate == null
                                        ? 'Start Date'
                                        : '${_startDate!.month}/${_startDate!.day}/${_startDate!.year}',
                                  ),
                                  style: OutlinedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () => _selectDate(context, false),
                                  icon: const Icon(Icons.calendar_today),
                                  label: Text(
                                    _endDate == null
                                        ? 'End Date'
                                        : '${_endDate!.month}/${_endDate!.day}/${_endDate!.year}',
                                  ),
                                  style: OutlinedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),

                          // Job Settings Section
                          AdaptiveFormSection(
                            title: 'Invoice Settings',
                            icon: Icons.settings,
                            children: [
                              // Description text
                              Container(
                                padding: EdgeInsets.all(
                                  displayProvider.isOfficeMode ? 10 : 12,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.blue.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.blue.shade200,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      color: Colors.blue,
                                      size:
                                          displayProvider.isOfficeMode
                                              ? 16
                                              : 20,
                                    ),
                                    SizedBox(
                                      width:
                                          displayProvider.isOfficeMode ? 6 : 8,
                                    ),
                                    Expanded(
                                      child: Text(
                                        _isEditing
                                            ? 'Configure which items should be available for inclusion in invoices for this job.'
                                            : 'These settings are inherited from your global defaults but can be customized for this job.',
                                        style: TextStyle(
                                          fontSize:
                                              displayProvider.isOfficeMode
                                                  ? 11
                                                  : 13,
                                          color: Colors.blue.shade700,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: displayProvider.isOfficeMode ? 12 : 16,
                              ),

                              // Sync Expenses Toggle
                              _buildJobSettingToggle(
                                displayProvider,
                                'Sync Job Expenses',
                                'Include job expenses when creating invoices',
                                _syncExpenses,
                                Icons.receipt_long,
                                (value) =>
                                    setState(() => _syncExpenses = value),
                              ),

                              // Sync Mileage Toggle
                              _buildJobSettingToggle(
                                displayProvider,
                                'Sync Mileage',
                                'Include mileage entries when creating invoices',
                                _syncMileage,
                                Icons.directions_car,
                                (value) => setState(() => _syncMileage = value),
                              ),

                              // Sync Labor Costs Toggle
                              _buildJobSettingToggle(
                                displayProvider,
                                'Sync Hours/Labor',
                                'Include time logs when creating invoices',
                                _syncLaborCosts,
                                Icons.access_time,
                                (value) =>
                                    setState(() => _syncLaborCosts = value),
                              ),

                              // Sync Estimate Items Toggle
                              _buildJobSettingToggle(
                                displayProvider,
                                'Sync Estimate Items',
                                'Include estimate line items when creating invoices',
                                _syncEstimateItems,
                                Icons.description,
                                (value) =>
                                    setState(() => _syncEstimateItems = value),
                              ),

                              // Summarize Mileage Toggle
                              _buildJobSettingToggle(
                                displayProvider,
                                'Summarize Mileage',
                                'Show mileage as single summary line item',
                                _summarizeMileage,
                                Icons.summarize,
                                (value) =>
                                    setState(() => _summarizeMileage = value),
                              ),

                              // Summarize Hours Toggle
                              _buildJobSettingToggle(
                                displayProvider,
                                'Summarize Hours',
                                'Show hours as single summary line item',
                                _summarizeHours,
                                Icons.schedule,
                                (value) =>
                                    setState(() => _summarizeHours = value),
                              ),

                              // Default Invoice Due Days
                              SizedBox(
                                height: displayProvider.isOfficeMode ? 8 : 12,
                              ),
                              Row(
                                children: [
                                  Icon(
                                    Icons.calendar_today,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    size:
                                        displayProvider.isOfficeMode ? 16 : 20,
                                  ),
                                  SizedBox(
                                    width: displayProvider.isOfficeMode ? 6 : 8,
                                  ),
                                  Expanded(
                                    child: Text(
                                      'Default Invoice Due Days',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize:
                                            displayProvider.isOfficeMode
                                                ? 12
                                                : 14,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 80,
                                    child: TextFormField(
                                      initialValue:
                                          _defaultInvoiceDueDays.toString(),
                                      keyboardType: TextInputType.number,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize:
                                            displayProvider.isOfficeMode
                                                ? 12
                                                : 14,
                                      ),
                                      decoration: InputDecoration(
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            8.0,
                                          ),
                                        ),
                                        contentPadding: EdgeInsets.symmetric(
                                          horizontal:
                                              displayProvider.isOfficeMode
                                                  ? 6
                                                  : 8,
                                          vertical:
                                              displayProvider.isOfficeMode
                                                  ? 8
                                                  : 12,
                                        ),
                                        isDense: displayProvider.isOfficeMode,
                                      ),
                                      onChanged: (value) {
                                        final days = int.tryParse(value);
                                        if (days != null && days > 0) {
                                          setState(() {
                                            _defaultInvoiceDueDays = days;
                                          });
                                        }
                                      },
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return 'Required';
                                        }
                                        final days = int.tryParse(value);
                                        if (days == null || days <= 0) {
                                          return 'Invalid';
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const SizedBox(height: 24),
                          // Save button and voice input button
                          Row(
                            children: [
                              Expanded(
                                child: Consumer<LoadingStateProvider>(
                                  builder: (context, loadingProvider, child) {
                                    return CustomButton(
                                      text:
                                          _isEditing
                                              ? 'Update Job'
                                              : 'Create Job',
                                      onPressed: _saveJob,
                                      isLoading: loadingProvider.isLoading(
                                        'saveJob',
                                      ),
                                    );
                                  },
                                ),
                              ),
                              if (!_isRecording)
                                Padding(
                                  padding: const EdgeInsets.only(left: 16.0),
                                  child: ElevatedButton.icon(
                                    icon: const Icon(Icons.mic),
                                    label: const Text('Voice Input'),
                                    onPressed: _startRecording,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          Theme.of(
                                            context,
                                          ).colorScheme.secondary,
                                      foregroundColor:
                                          Theme.of(
                                            context,
                                          ).colorScheme.onSecondary,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
    );
  }

  // Helper method to build job setting toggle widgets
  Widget _buildJobSettingToggle(
    DisplaySettingsProvider displayProvider,
    String title,
    String subtitle,
    bool value,
    IconData icon,
    ValueChanged<bool> onChanged,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: Theme.of(context).colorScheme.primary,
                    size: displayProvider.isOfficeMode ? 16 : 20,
                  ),
                  SizedBox(width: displayProvider.isOfficeMode ? 6 : 8),
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: displayProvider.isOfficeMode ? 12 : 14,
                    ),
                  ),
                ],
              ),
              SizedBox(height: displayProvider.isOfficeMode ? 2 : 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: displayProvider.isOfficeMode ? 10 : 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: Theme.of(context).colorScheme.primary,
        ),
      ],
    );
  }
}
