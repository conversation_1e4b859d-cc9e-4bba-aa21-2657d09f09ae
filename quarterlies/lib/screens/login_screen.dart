import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/services/auth_service.dart';
import 'package:quarterlies/services/data_repository.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';
import 'package:quarterlies/widgets/custom_widgets.dart';
import 'package:quarterlies/utils/error_handler.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/utils/input_validators.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _authService = AuthService();
  final _dataRepository = DataRepository();
  String? _errorMessage;
  bool _rememberMe = false; // Added for "Remember Me" functionality

  @override
  void initState() {
    super.initState();
    _loadRememberedCredentials();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Load remembered credentials if remember me is enabled
  Future<void> _loadRememberedCredentials() async {
    try {
      final isRememberMeEnabled = await _authService.isRememberMeEnabled();
      if (isRememberMeEnabled) {
        final credentials = await _authService.getRememberedCredentials();
        final email = credentials['email'];
        final password = credentials['password'];

        if (email != null && password != null && mounted) {
          setState(() {
            _emailController.text = email;
            _passwordController.text = password;
            _rememberMe = true;
          });
        }
      }
    } catch (e) {
      // Silently fail - remember me is not critical functionality
    }
  }

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      final loadingProvider = Provider.of<LoadingStateProvider>(
        context,
        listen: false,
      );

      try {
        await loadingProvider.executeWithLoading(
          'login',
          () async {
            setState(() {
              _errorMessage = null;
            });

            await _authService.signInWithRememberMe(
              email: _emailController.text.trim(),
              password: _passwordController.text,
              rememberMe: _rememberMe,
            );

            if (mounted) {
              // Check if user has completed onboarding
              final isOnboardingComplete =
                  await _dataRepository.isOnboardingComplete();

              if (mounted) {
                if (isOnboardingComplete) {
                  // Navigate to home page after successful login
                  Navigator.pushReplacementNamed(context, '/home');
                } else {
                  // Navigate to onboarding flow
                  Navigator.pushReplacementNamed(context, '/onboarding');
                }
              }
            }
          },
          message: 'Signing in...',
          errorMessage: 'Failed to sign in',
        );
      } catch (e) {
        if (mounted) {
          final appError = AppError.fromException(
            e,
            context: {
              'operation': 'login',
              'email': _emailController.text.trim(),
              'rememberMe': _rememberMe,
            },
          );
          ErrorHandler.logError(appError);

          setState(() {
            _errorMessage = ErrorHandler.getUserFriendlyMessage(appError);
          });

          // Show error dialog for authentication errors
          if (appError.type == ErrorType.authentication) {
            ErrorDisplay.showErrorDialog(
              context,
              appError,
              onRetry: () => _login(),
            );
          } else {
            // Show snackbar for other errors
            ErrorDisplay.showSnackBar(
              context,
              appError,
              onRetry: () => _login(),
            );
          }
        }
      }
    }
  }

  void _navigateToPasswordReset() {
    Navigator.pushNamed(context, '/password-reset');
  }

  void _toggleRememberMe(bool? value) {
    final newValue = value ?? false;
    setState(() {
      _rememberMe = newValue;
    });

    // Clear remembered credentials if remember me is disabled
    if (!newValue) {
      _authService.clearRememberedCredentials();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: ResponsiveTitle(
              'Login',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              minFontSize: 16.0,
              maxFontSize: 24.0,
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            elevation: 4,
            toolbarHeight: displayProvider.isOfficeMode ? 56 : 64,
          ),
          body: ResponsiveLayout(
            padding: EdgeInsets.symmetric(
              horizontal: displayProvider.isOfficeMode ? 16.0 : 20.0,
              vertical: displayProvider.isOfficeMode ? 20.0 : 24.0,
            ),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: displayProvider.isOfficeMode ? 24 : 32),
                  // App logo or icon for better visual identity
                  Icon(
                    Icons.account_balance,
                    size: displayProvider.isOfficeMode ? 56 : 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 20 : 24),
                  ResponsiveTitle(
                    'Welcome Back',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                    minFontSize: 20.0,
                    maxFontSize: 32.0,
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 24 : 32),
                  CustomTextField(
                    controller: _emailController,
                    labelText: 'Email',
                    hintText: 'Enter your email',
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) => InputValidators.validateEmail(value),
                  ),
                  CustomTextField(
                    controller: _passwordController,
                    labelText: 'Password',
                    hintText: 'Enter your password',
                    obscureText: true,
                    validator:
                        (value) => InputValidators.validatePassword(value),
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
                  context.isMobile
                      ? Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: _rememberMe,
                                onChanged: _toggleRememberMe,
                              ),
                              Expanded(
                                child: ResponsiveLabel(
                                  'Remember Me',
                                  minFontSize: 11.0,
                                  maxFontSize: 16.0,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: displayProvider.isOfficeMode ? 8 : 12,
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton(
                              onPressed: _navigateToPasswordReset,
                              child: Text(
                                'Forgot Password?',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 13 : 14,
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                      : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Checkbox(
                                  value: _rememberMe,
                                  onChanged: _toggleRememberMe,
                                ),
                                Flexible(
                                  child: Text(
                                    'Remember Me',
                                    style: TextStyle(
                                      fontSize:
                                          displayProvider.isOfficeMode
                                              ? 13
                                              : 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          TextButton(
                            onPressed: _navigateToPasswordReset,
                            child: Text(
                              'Forgot Password?',
                              style: TextStyle(
                                fontSize:
                                    displayProvider.isOfficeMode ? 13 : 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                  if (_errorMessage != null)
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: displayProvider.isOfficeMode ? 12.0 : 16.0,
                      ),
                      child: Container(
                        padding: EdgeInsets.all(
                          displayProvider.isOfficeMode ? 10 : 12,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade200),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red.shade700,
                              size: displayProvider.isOfficeMode ? 18 : 20,
                            ),
                            SizedBox(
                              width: displayProvider.isOfficeMode ? 6 : 8,
                            ),
                            Expanded(
                              child: Text(
                                _errorMessage!,
                                style: TextStyle(
                                  color: Colors.red.shade700,
                                  fontSize:
                                      displayProvider.isOfficeMode ? 13 : 14,
                                ),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.close),
                              iconSize: displayProvider.isOfficeMode ? 16 : 18,
                              color: Colors.red.shade700,
                              onPressed: () {
                                setState(() {
                                  _errorMessage = null;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  Consumer<LoadingStateProvider>(
                    builder: (context, loadingProvider, child) {
                      return CustomButton(
                        text: 'Login',
                        onPressed: _login,
                        isLoading: loadingProvider.isLoading('login'),
                      );
                    },
                  ),
                  SizedBox(height: displayProvider.isOfficeMode ? 12 : 16),
                  TextButton(
                    onPressed: () {
                      Navigator.pushNamed(context, '/signup');
                    },
                    child: Text(
                      'Don\'t have an account? Sign up',
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 13 : 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
