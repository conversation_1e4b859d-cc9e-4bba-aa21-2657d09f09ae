import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';

class WelcomeStep extends StatelessWidget {
  final VoidCallback onNext;
  final bool isLoading;

  const WelcomeStep({super.key, required this.onNext, required this.isLoading});

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 20.0),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top -
                    MediaQuery.of(context).padding.bottom -
                    (displayProvider.isOfficeMode ? 32.0 : 40.0),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // App logo/icon
                  Container(
                    width: displayProvider.isOfficeMode ? 60 : 80,
                    height: displayProvider.isOfficeMode ? 60 : 80,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      borderRadius: BorderRadius.circular(
                        displayProvider.isOfficeMode ? 12 : 16,
                      ),
                    ),
                    child: Icon(
                      Icons.business_center,
                      size: displayProvider.isOfficeMode ? 30 : 40,
                      color: Colors.white,
                    ),
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 16.0 : 20.0),

                  // Welcome title
                  Text(
                    'Welcome to Quarterlies!',
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 20 : 24,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 8.0 : 12.0),

                  // Welcome description
                  Text(
                    'Your comprehensive business management solution for contractors and small businesses.',
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 14 : 16,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.8),
                      height: 1.3,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 16.0 : 20.0),

                  // Features list
                  Container(
                    padding: EdgeInsets.all(
                      displayProvider.isOfficeMode ? 12.0 : 16.0,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(
                          context,
                        ).colorScheme.outline.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'What you can do with Quarterlies:',
                          style: TextStyle(
                            fontSize: displayProvider.isOfficeMode ? 14 : 16,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        SizedBox(
                          height: displayProvider.isOfficeMode ? 8.0 : 12.0,
                        ),
                        _buildFeatureItem(
                          context,
                          displayProvider,
                          Icons.people,
                          'Manage customers and jobs',
                        ),
                        _buildFeatureItem(
                          context,
                          displayProvider,
                          Icons.receipt_long,
                          'Create estimates, contracts & invoices',
                        ),
                        _buildFeatureItem(
                          context,
                          displayProvider,
                          Icons.access_time,
                          'Track time, expenses & mileage',
                        ),
                        _buildFeatureItem(
                          context,
                          displayProvider,
                          Icons.analytics,
                          'Generate business reports',
                        ),
                        _buildFeatureItem(
                          context,
                          displayProvider,
                          Icons.cloud_off,
                          'Work offline with automatic sync',
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 16.0 : 20.0),

                  // Setup description
                  Text(
                    'Let\'s get you set up! We\'ll collect some basic information to personalize your experience.',
                    style: TextStyle(
                      fontSize: displayProvider.isOfficeMode ? 12 : 14,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.7),
                      height: 1.3,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: displayProvider.isOfficeMode ? 20.0 : 24.0),

                  // Get started button
                  SizedBox(
                    width: double.infinity,
                    height: displayProvider.isOfficeMode ? 48 : 56,
                    child: ElevatedButton(
                      onPressed: isLoading ? null : onNext,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child:
                          isLoading
                              ? SizedBox(
                                height: displayProvider.isOfficeMode ? 20 : 24,
                                width: displayProvider.isOfficeMode ? 20 : 24,
                                child: const CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : Text(
                                'Get Started',
                                style: TextStyle(
                                  fontSize:
                                      displayProvider.isOfficeMode ? 16 : 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeatureItem(
    BuildContext context,
    DisplaySettingsProvider displayProvider,
    IconData icon,
    String text,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: displayProvider.isOfficeMode ? 2.0 : 3.0,
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: displayProvider.isOfficeMode ? 16 : 18,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(width: displayProvider.isOfficeMode ? 6.0 : 8.0),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: displayProvider.isOfficeMode ? 12 : 14,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
