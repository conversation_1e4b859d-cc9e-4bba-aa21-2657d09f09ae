import 'package:flutter/material.dart';
import 'package:quarterlies/screens/customers/customer_form_screen.dart';
import 'package:quarterlies/screens/expenses/expense_form_screen.dart';
import 'package:quarterlies/screens/estimates/estimate_form_screen.dart';
import 'package:quarterlies/screens/time_logs/time_log_form_screen.dart';
import 'package:quarterlies/screens/mileage/mileage_form_screen.dart';
import 'package:quarterlies/services/recent_data_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:quarterlies/services/auth_service.dart';

class BottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const BottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<ConnectivityResult>>(
      stream: Connectivity().onConnectivityChanged,
      builder: (context, snapshot) {
        final isOffline =
            snapshot.data?.isEmpty ??
            false ||
                (snapshot.data?.length == 1 &&
                    snapshot.data?.first == ConnectivityResult.none);
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isOffline)
              Container(
                color: Colors.red,
                padding: const EdgeInsets.all(8.0),
                child: const Text(
                  'You are offline',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            BottomAppBar(
              shape: const CircularNotchedRectangle(),
              notchMargin: 10.0,
              elevation: 8.0,
              color: Theme.of(context).colorScheme.surface,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildNavItem(context, 0, Icons.dashboard, 'Dashboard'),
                      _buildNavItem(context, 1, Icons.home, 'Home'),
                      _buildNavItem(context, 2, Icons.work, 'Jobs'),
                      const SizedBox(width: 40),
                      _buildNavItem(context, 3, Icons.receipt_long, 'Invoices'),
                      _buildNavItem(context, 4, Icons.bar_chart, 'Reports'),
                      _buildNavItem(context, 5, Icons.draw, 'Signatures'),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    int index,
    IconData icon,
    String label,
  ) {
    final isSelected = currentIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () async {
          final authService = AuthService();
          final isAuthenticated = authService.isLoggedIn;
          if (!isAuthenticated) {
            if (context.mounted) {
              Navigator.pushReplacementNamed(context, '/login');
            }
            return;
          }
          onTap(index);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(
                          context,
                        ).colorScheme.onSurface.withAlpha(179),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color:
                      isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(
                            context,
                          ).colorScheme.onSurface.withAlpha(179),
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class QuickAddButton extends StatelessWidget {
  const QuickAddButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: const Color(0xFFFF6700), // Accent color #ff6700
      elevation: 8.0,
      shape: const CircleBorder(),
      child: const Icon(Icons.add, size: 32, color: Colors.white),
      onPressed: () {
        _showQuickAddMenu(context);
      },
    );
  }

  void _showQuickAddMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => const QuickAddMenu(),
    );
  }
}

class QuickAddMenu extends StatefulWidget {
  const QuickAddMenu({super.key});

  @override
  State<QuickAddMenu> createState() => _QuickAddMenuState();
}

class _QuickAddMenuState extends State<QuickAddMenu> {
  final RecentDataService _recentDataService = RecentDataService();
  List<Map<String, dynamic>> _recentJobs = [];
  List<Map<String, dynamic>> _recentCustomers = [];

  @override
  void initState() {
    super.initState();
    _fetchRecentData();
  }

  Future<void> _fetchRecentData() async {
    try {
      final jobs = await _recentDataService.getRecentJobs();
      final customers = await _recentDataService.getRecentCustomers();

      if (!mounted) return; // Add mounted check here

      setState(() {
        _recentJobs = jobs;
        _recentCustomers = customers;
      });
    } catch (e) {
      if (!mounted) return; // Add mounted check here
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Add',
            style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAddOption(
                context,
                Icons.receipt,
                'Expense',
                () => _navigateToExpenseForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.person_add,
                'Customer',
                () => _navigateToCustomerForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.description,
                'Estimate',
                () => _navigateToEstimateForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.timer,
                'Time Log',
                () => _navigateToTimeLogForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.directions_car,
                'Mileage',
                () => _navigateToMileageForm(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAddOption(
                context,
                Icons.business_center,
                'Overhead',
                () => _navigateToOverheadExpenseForm(context),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Recent jobs section
          if (_recentJobs.isNotEmpty) ...[
            const Text(
              'Recent Jobs',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _recentJobs.length,
                itemBuilder: (context, index) {
                  final job = _recentJobs[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: InkWell(
                      onTap: () async {
                        // Update job usage count
                        _recentDataService.updateJobUsage(job['id']);

                        // Get default values based on recent entries
                        final defaults =
                            await _recentDataService.getTimeLogDefaults();

                        if (!mounted) return;

                        // Store context in local variable to ensure it's valid
                        final currentContext = context;
                        if (!mounted) return;
                        if (!currentContext.mounted) return;

                        Navigator.pop(currentContext); // Close the bottom sheet
                        Navigator.push(
                          currentContext,
                          MaterialPageRoute(
                            builder:
                                (context) => TimeLogFormScreen(
                                  initialJobId: job['id'],
                                  initialDefaults: defaults,
                                ),
                          ),
                        );
                      },
                      child: Container(
                        width: 120,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(
                                context,
                              ).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              job['name'],
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (job['customer'] != null)
                              Text(
                                job['customer']['name'],
                                style: TextStyle(
                                  fontSize: 12,
                                  color:
                                      Theme.of(
                                        context,
                                      ).colorScheme.onSurfaceVariant,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Recent customers section
          if (_recentCustomers.isNotEmpty) ...[
            const Text(
              'Recent Customers',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _recentCustomers.length,
                itemBuilder: (context, index) {
                  final customer = _recentCustomers[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: InkWell(
                      onTap: () async {
                        // Update customer usage count
                        _recentDataService.updateCustomerUsage(customer['id']);

                        // Get default values based on recent entries
                        final defaults =
                            await _recentDataService.getEstimateDefaults();

                        if (!mounted) return;

                        // Store context in local variable to ensure it's valid
                        final currentContext = context;
                        if (!mounted) return;
                        if (!currentContext.mounted) return;

                        Navigator.pop(currentContext); // Close the bottom sheet
                        Navigator.push(
                          currentContext,
                          MaterialPageRoute(
                            builder:
                                (context) => EstimateFormScreen(
                                  initialCustomerId: customer['id'],
                                  initialDefaults: defaults,
                                ),
                          ),
                        );
                      },
                      child: Container(
                        width: 120,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(
                                context,
                              ).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              customer['name'],
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              customer['email'] ?? '',
                              style: TextStyle(
                                fontSize: 12,
                                color:
                                    Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickAddOption(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFFF6700).withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: const Color(0xFFFF6700), size: 28),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToExpenseForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    // Get default values based on recent entries
    final defaults = await _recentDataService.getExpenseDefaults();

    if (!mounted) return;
    if (!currentContext.mounted) return;

    // If we have recent jobs, show job selection dialog
    if (_recentJobs.isNotEmpty) {
      if (!mounted) return; // Add mounted check before showing dialog
      if (!currentContext.mounted) return;

      final selectedJob = await _showJobSelectionDialog(currentContext);
      if (!mounted) return; // Existing mounted check after dialog
      if (!currentContext.mounted) return;

      if (selectedJob != null) {
        if (!mounted) return; // Add mounted check before navigation
        if (!currentContext.mounted) return;

        Navigator.push(
          currentContext,
          MaterialPageRoute(
            builder:
                (context) => ExpenseFormScreen(
                  initialJobId: selectedJob['id'],
                  initialDefaults: defaults,
                ),
          ),
        );
        return;
      }
    }

    // If no job selected or no recent jobs, just navigate to form
    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(
        builder: (context) => ExpenseFormScreen(initialDefaults: defaults),
      ),
    );
  }

  void _navigateToCustomerForm(BuildContext context) {
    Navigator.pop(context); // Close the bottom sheet
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CustomerFormScreen()),
    );
  }

  void _navigateToEstimateForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    // Get default values based on recent entries
    final defaults = await _recentDataService.getEstimateDefaults();

    if (!mounted) return;
    if (!currentContext.mounted) return;

    // If we have recent customers, show customer selection dialog
    if (_recentCustomers.isNotEmpty) {
      if (!mounted) return; // Add mounted check before showing dialog
      if (!currentContext.mounted) return;

      final selectedCustomer = await _showCustomerSelectionDialog(
        currentContext,
      );
      if (!mounted) return; // Existing mounted check after dialog
      if (!currentContext.mounted) return;

      if (selectedCustomer != null) {
        // Update customer usage count
        _recentDataService.updateCustomerUsage(selectedCustomer['id']);

        if (!mounted) return;
        if (!currentContext.mounted) return;

        Navigator.push(
          currentContext,
          MaterialPageRoute(
            builder:
                (context) => EstimateFormScreen(
                  initialCustomerId: selectedCustomer['id'],
                  initialDefaults: defaults,
                ),
          ),
        );
        return;
      }
    }

    // If no customer selected or no recent customers, just navigate to form
    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(
        builder: (context) => EstimateFormScreen(initialDefaults: defaults),
      ),
    );
  }

  void _navigateToTimeLogForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    // Get default values based on recent entries
    final defaults = await _recentDataService.getTimeLogDefaults();

    if (!mounted) return;
    if (!currentContext.mounted) return;

    // If we have recent jobs, show job selection dialog
    if (_recentJobs.isNotEmpty) {
      if (!mounted) return; // Add mounted check before showing dialog
      if (!currentContext.mounted) return;

      final selectedJob = await _showJobSelectionDialog(currentContext);
      if (!mounted) return; // Existing mounted check after dialog
      if (!currentContext.mounted) return;

      if (selectedJob != null) {
        // Update job usage count
        _recentDataService.updateJobUsage(selectedJob['id']);

        if (!mounted) return;
        if (!currentContext.mounted) return;

        Navigator.push(
          currentContext,
          MaterialPageRoute(
            builder:
                (context) => TimeLogFormScreen(
                  initialJobId: selectedJob['id'],
                  initialDefaults: defaults,
                ),
          ),
        );
        return;
      }
    }

    // If no job selected or no recent jobs, just navigate to form
    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(
        builder: (context) => TimeLogFormScreen(initialDefaults: defaults),
      ),
    );
  }

  void _navigateToMileageForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    if (!mounted) return;
    if (!currentContext.mounted) return;

    // If we have recent jobs, show job selection dialog
    if (_recentJobs.isNotEmpty) {
      if (!mounted) return; // Add mounted check before showing dialog
      if (!currentContext.mounted) return;

      final selectedJob = await _showJobSelectionDialog(currentContext);
      if (!mounted) return; // Existing mounted check after dialog
      if (!currentContext.mounted) return;

      if (selectedJob != null) {
        // Update job usage count
        _recentDataService.updateJobUsage(selectedJob['id']);

        if (!mounted) return;
        if (!currentContext.mounted) return;

        Navigator.push(
          currentContext,
          MaterialPageRoute(
            builder: (context) => MileageFormScreen(jobId: selectedJob['id']),
          ),
        );
        return;
      }
    }

    // If no job selected or no recent jobs, just navigate to form
    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(builder: (context) => const MileageFormScreen()),
    );
  }

  void _navigateToOverheadExpenseForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    // Get default values based on recent entries
    final defaults = await _recentDataService.getExpenseDefaults();

    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(
        builder:
            (context) => ExpenseFormScreen(
              initialDefaults: defaults,
              isOverheadExpense: true, // Force overhead expense creation
            ),
      ),
    );
  }

  Future<Map<String, dynamic>?> _showJobSelectionDialog(
    BuildContext parentContext, // Renamed parameter
  ) async {
    return showDialog<Map<String, dynamic>>(
      context: parentContext,
      builder:
          (dialogContext) => AlertDialog(
            title: const Text('Select Job'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _recentJobs.length,
                itemBuilder: (context, index) {
                  final job = _recentJobs[index];
                  return ListTile(
                    title: Text(job['name']),
                    subtitle: Text(job['customer']?['name'] ?? ''),
                    onTap: () {
                      if (!parentContext.mounted) {
                        return; // Check parent context
                      }
                      Navigator.of(dialogContext).pop(job);
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  if (!parentContext.mounted) return; // Check parent context
                  Navigator.of(dialogContext).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  if (!parentContext.mounted) return; // Check parent context
                  Navigator.of(dialogContext).pop(null);
                },
                child: const Text('No Job'),
              ),
            ],
          ),
    );
  }

  Future<Map<String, dynamic>?> _showCustomerSelectionDialog(
    BuildContext parentContext, // Renamed parameter
  ) async {
    return showDialog<Map<String, dynamic>>(
      context: parentContext,
      builder:
          (dialogContext) => AlertDialog(
            title: const Text('Select Customer'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _recentCustomers.length,
                itemBuilder: (context, index) {
                  final customer = _recentCustomers[index];
                  return ListTile(
                    title: Text(customer['name']),
                    subtitle: Text(customer['email'] ?? ''),
                    onTap: () {
                      if (!parentContext.mounted) {
                        return; // Check parent context
                      }
                      // Update customer usage count
                      _recentDataService.updateCustomerUsage(customer['id']);
                      Navigator.of(dialogContext).pop(customer);
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  if (!parentContext.mounted) return; // Check parent context
                  Navigator.of(dialogContext).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  if (!parentContext.mounted) return; // Check parent context
                  Navigator.of(dialogContext).pop(null);
                },
                child: const Text('New Customer'),
              ),
            ],
          ),
    );
  }
}
