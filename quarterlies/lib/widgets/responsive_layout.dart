import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/utils/responsive_helper.dart';

/// A comprehensive responsive layout widget that ensures:
/// - Full width/height utilization without unwanted padding/bars
/// - Proper adaptation to all screen sizes and orientations
/// - Visual consistency across device aspect ratios
/// - Proper SafeArea handling
/// - Keyboard avoidance
class ResponsiveLayout extends StatelessWidget {
  final Widget child;
  final bool useSafeArea;
  final bool avoidKeyboard;
  final bool fillMaxWidth;
  final bool fillMaxHeight;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final bool adaptToOrientation;
  final bool adaptToDisplayMode;
  final Color? backgroundColor;
  final bool enableScrolling;
  final ScrollPhysics? scrollPhysics;

  const ResponsiveLayout({
    super.key,
    required this.child,
    this.useSafeArea = true,
    this.avoidKeyboard = true,
    this.fillMaxWidth = true,
    this.fillMaxHeight = true,
    this.padding,
    this.margin,
    this.adaptToOrientation = true,
    this.adaptToDisplayMode = true,
    this.backgroundColor,
    this.enableScrolling = true,
    this.scrollPhysics,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        Widget content = _buildContent(context, displayProvider);

        // Apply SafeArea if needed
        if (useSafeArea) {
          content = SafeArea(child: content);
        }

        // Apply keyboard avoidance if needed
        if (avoidKeyboard) {
          content = _buildKeyboardAvoiding(context, content);
        }

        // Apply full screen constraints
        content = _buildFullScreenContainer(context, content);

        return content;
      },
      child: child,
    );
  }

  Widget _buildContent(BuildContext context, DisplaySettingsProvider displayProvider) {
    final adaptedPadding = _getAdaptedPadding(context, displayProvider);
    final adaptedMargin = _getAdaptedMargin(context, displayProvider);

    Widget content = Container(
      width: fillMaxWidth ? double.infinity : null,
      height: fillMaxHeight ? double.infinity : null,
      padding: adaptedPadding,
      margin: adaptedMargin,
      decoration: backgroundColor != null 
          ? BoxDecoration(color: backgroundColor)
          : null,
      child: child,
    );

    // Add scrolling if enabled
    if (enableScrolling) {
      content = SingleChildScrollView(
        physics: scrollPhysics ?? const AlwaysScrollableScrollPhysics(),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: _getMinContentHeight(context),
            minWidth: fillMaxWidth ? double.infinity : 0,
          ),
          child: content,
        ),
      );
    }

    return content;
  }

  Widget _buildKeyboardAvoiding(BuildContext context, Widget content) {
    return KeyboardAvoidingView(
      behavior: KeyboardAvoidingBehavior.padding,
      child: content,
    );
  }

  Widget _buildFullScreenContainer(BuildContext context, Widget content) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: content,
    );
  }

  EdgeInsets _getAdaptedPadding(BuildContext context, DisplaySettingsProvider displayProvider) {
    if (padding != null) return padding!;

    double basePadding = 16.0;

    // Adapt to display mode
    if (adaptToDisplayMode) {
      basePadding = displayProvider.isOfficeMode ? 12.0 : 20.0;
    }

    // Adapt to screen size
    if (ResponsiveHelper.isTablet(context)) {
      basePadding *= 1.2;
    } else if (ResponsiveHelper.isDesktop(context)) {
      basePadding *= 1.5;
    }

    // Adapt to orientation
    if (adaptToOrientation && ResponsiveHelper.isLandscape(context)) {
      return EdgeInsets.symmetric(
        horizontal: basePadding * 1.5,
        vertical: basePadding * 0.8,
      );
    }

    return EdgeInsets.all(basePadding);
  }

  EdgeInsets _getAdaptedMargin(BuildContext context, DisplaySettingsProvider displayProvider) {
    if (margin != null) return margin!;
    return EdgeInsets.zero;
  }

  double _getMinContentHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final safeAreaTop = MediaQuery.of(context).padding.top;
    final safeAreaBottom = MediaQuery.of(context).padding.bottom;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return screenHeight - safeAreaTop - safeAreaBottom - keyboardHeight;
  }
}

/// Custom KeyboardAvoidingView that works better across different platforms
class KeyboardAvoidingView extends StatelessWidget {
  final Widget child;
  final KeyboardAvoidingBehavior behavior;

  const KeyboardAvoidingView({
    super.key,
    required this.child,
    this.behavior = KeyboardAvoidingBehavior.padding,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: behavior == KeyboardAvoidingBehavior.padding
          ? EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom)
          : EdgeInsets.zero,
      child: child,
    );
  }
}

enum KeyboardAvoidingBehavior {
  padding,
  resize,
  none,
}

/// A responsive container that adapts to different aspect ratios
class AspectRatioContainer extends StatelessWidget {
  final Widget child;
  final double? aspectRatio;
  final bool adaptToScreen;

  const AspectRatioContainer({
    super.key,
    required this.child,
    this.aspectRatio,
    this.adaptToScreen = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!adaptToScreen || aspectRatio == null) {
      return child;
    }

    final screenSize = MediaQuery.of(context).size;
    final screenAspectRatio = screenSize.width / screenSize.height;

    // Adjust container based on screen aspect ratio
    if (screenAspectRatio > 2.0) {
      // Ultra-wide screens
      return Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: screenSize.height * 1.5,
          ),
          child: AspectRatio(
            aspectRatio: aspectRatio!,
            child: child,
          ),
        ),
      );
    } else if (screenAspectRatio < 0.6) {
      // Very tall/narrow screens
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: AspectRatio(
          aspectRatio: aspectRatio!,
          child: child,
        ),
      );
    }

    return AspectRatio(
      aspectRatio: aspectRatio!,
      child: child,
    );
  }
}

/// A responsive grid that adapts to screen size and orientation
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? crossAxisCount;
  final double? childAspectRatio;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final bool adaptToOrientation;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.crossAxisCount,
    this.childAspectRatio,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.adaptToOrientation = true,
  });

  @override
  Widget build(BuildContext context) {
    final adaptedCrossAxisCount = _getAdaptedCrossAxisCount(context);
    final adaptedChildAspectRatio = _getAdaptedChildAspectRatio(context);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: adaptedCrossAxisCount,
        childAspectRatio: adaptedChildAspectRatio,
        mainAxisSpacing: mainAxisSpacing,
        crossAxisSpacing: crossAxisSpacing,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }

  int _getAdaptedCrossAxisCount(BuildContext context) {
    if (crossAxisCount != null) return crossAxisCount!;

    final screenWidth = MediaQuery.of(context).size.width;
    final isLandscape = ResponsiveHelper.isLandscape(context);

    if (ResponsiveHelper.isDesktop(context)) {
      return isLandscape ? 4 : 3;
    } else if (ResponsiveHelper.isTablet(context)) {
      return isLandscape ? 3 : 2;
    } else {
      return isLandscape ? 2 : 1;
    }
  }

  double _getAdaptedChildAspectRatio(BuildContext context) {
    if (childAspectRatio != null) return childAspectRatio!;

    final isLandscape = ResponsiveHelper.isLandscape(context);
    return isLandscape ? 1.5 : 1.0;
  }
}
